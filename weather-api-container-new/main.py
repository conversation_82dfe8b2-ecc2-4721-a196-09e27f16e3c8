#!/usr/bin/env python3
"""
Weather API Container - FastAPI Application

Serves weather data via FastAPI with Twilio integration.
Fetches weather data every 2 minutes and generates AWOS MP3 files.
"""

import asyncio
import os
import logging
import tempfile
from pathlib import Path
from typing import Optional
import uvicorn
from fastapi import FastAP<PERSON>, Response, HTTPException, UploadFile, File, Form, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import json
import time
import shutil
import glob
from datetime import datetime, timedelta

from weather_service import WeatherService
from s3_upload_manager import create_s3_upload_manager
from recording_schemas import RecordingsListResponse, ErrorResponse
from recordings_service import get_recordings_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global weather service instance
weather_service: Optional[WeatherService] = None

# S3 upload manager for recording uploads
s3_upload_manager = None

# HLS storage configuration
HLS_STORAGE_PATH = Path("/app/hls_storage")
HLS_STORAGE_PATH.mkdir(parents=True, exist_ok=True)

# HLS retention settings
HLS_RETENTION_HOURS = int(os.getenv('HLS_RETENTION_HOURS', '24'))  # Keep segments for 24 hours by default
HLS_CLEANUP_INTERVAL_MINUTES = int(os.getenv('HLS_CLEANUP_INTERVAL_MINUTES', '60'))  # Cleanup every hour

async def cleanup_old_hls_segments():
    """
    Background task to clean up old HLS segments and playlists.
    Removes segments older than HLS_RETENTION_HOURS to prevent storage bloat.
    """
    while True:
        try:
            current_time = time.time()
            cutoff_time = current_time - (HLS_RETENTION_HOURS * 3600)  # Convert hours to seconds

            total_removed = 0
            stations_cleaned = 0

            # Iterate through all station directories
            for station_dir in HLS_STORAGE_PATH.iterdir():
                if not station_dir.is_dir():
                    continue

                station_removed = 0

                # Clean up old segments (.ts files)
                for segment_file in station_dir.glob("*.ts"):
                    try:
                        if segment_file.stat().st_mtime < cutoff_time:
                            segment_file.unlink()
                            station_removed += 1
                            total_removed += 1
                    except Exception as e:
                        logger.warning(f"Failed to remove old segment {segment_file}: {e}")

                # Clean up old playlists if no recent segments exist
                playlist_file = station_dir / "playlist.m3u8"
                if playlist_file.exists():
                    try:
                        # Check if there are any recent segments
                        recent_segments = [
                            f for f in station_dir.glob("*.ts")
                            if f.stat().st_mtime >= cutoff_time
                        ]

                        # If no recent segments, remove the playlist too
                        if not recent_segments and playlist_file.stat().st_mtime < cutoff_time:
                            playlist_file.unlink()
                            logger.info(f"Removed stale playlist for station {station_dir.name}")
                    except Exception as e:
                        logger.warning(f"Failed to check/remove playlist {playlist_file}: {e}")

                # Remove empty station directories
                try:
                    if not any(station_dir.iterdir()):
                        station_dir.rmdir()
                        logger.info(f"Removed empty station directory: {station_dir.name}")
                except Exception as e:
                    logger.debug(f"Station directory {station_dir.name} not empty or removal failed: {e}")

                if station_removed > 0:
                    stations_cleaned += 1
                    logger.info(f"Cleaned {station_removed} old segments from station {station_dir.name}")

            if total_removed > 0:
                logger.info(f"HLS cleanup completed: removed {total_removed} old segments from {stations_cleaned} stations")
            else:
                logger.debug("HLS cleanup completed: no old segments to remove")

        except Exception as e:
            logger.error(f"Error during HLS cleanup: {e}")

        # Wait for next cleanup cycle
        await asyncio.sleep(HLS_CLEANUP_INTERVAL_MINUTES * 60)

# WebSocket connection manager for HLS uploads
class HLSConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"HLS WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"HLS WebSocket disconnected. Total connections: {len(self.active_connections)}")

hls_manager = HLSConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - startup and shutdown."""
    global weather_service, s3_upload_manager

    # Startup
    logger.info("Starting Weather API Container...")

    # Initialize weather service
    weather_service = WeatherService()

    # Initialize S3 upload manager for recording uploads
    s3_upload_manager = create_s3_upload_manager()
    if s3_upload_manager:
        logger.info("S3 upload manager initialized for recording uploads")
    else:
        logger.warning("S3 upload manager not initialized - recording uploads will be disabled")

    # Start background weather fetching task
    weather_task = asyncio.create_task(weather_service.start_weather_updates())

    # Start HLS cleanup task
    hls_cleanup_task = asyncio.create_task(cleanup_old_hls_segments())
    logger.info(f"Started HLS cleanup task (retention: {HLS_RETENTION_HOURS}h, interval: {HLS_CLEANUP_INTERVAL_MINUTES}m)")

    try:
        yield
    finally:
        # Shutdown
        logger.info("Shutting down Weather API Container...")
        if weather_service:
            await weather_service.stop()

        # Cancel background tasks
        weather_task.cancel()
        hls_cleanup_task.cancel()

        try:
            await weather_task
        except asyncio.CancelledError:
            pass

        try:
            await hls_cleanup_task
        except asyncio.CancelledError:
            pass

# Create FastAPI app with lifespan management
app = FastAPI(
    title="Weather API Container",
    description="Serves AWOS weather data with Twilio integration",
    version="1.0.0",
    lifespan=lifespan
)

# Configure allowed origins for CORS
ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "https://app.skytraces.com",
    "https://skytraces.com",
    # Add Twilio webhook origins if needed
    "https://webhooks.twilio.com",
]

# Add environment variable support for additional origins
import os
additional_origins = os.getenv("ADDITIONAL_CORS_ORIGINS", "")
if additional_origins:
    ALLOWED_ORIGINS.extend([origin.strip() for origin in additional_origins.split(",")])

# Add CORS middleware to handle cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "Range",
        "X-Requested-With",
    ],
    expose_headers=["Content-Range", "Accept-Ranges"],
)

@app.get("/")
async def root():
    """Root endpoint - health check."""
    return {
        "status": "healthy",
        "service": "Weather API Container",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    last_update = weather_service.get_last_update_time()
    return {
        "status": "healthy",
        "last_weather_update": last_update.isoformat() if last_update else None,
        "awos_file_exists": weather_service.awos_file_exists()
    }

@app.get("/4FL5/awos.mp3")
async def serve_awos_mp3():
    """Serve the current AWOS MP3 file."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    mp3_path = weather_service.get_awos_mp3_path()
    
    if not mp3_path.exists():
        raise HTTPException(status_code=404, detail="AWOS file not available")
    
    return FileResponse(
        path=str(mp3_path),
        media_type="audio/mpeg",
        filename="awos.mp3",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.post("/4FL5/twiml")
@app.get("/4FL5/twiml")
async def twilio_twiml():
    """Twilio TwiML endpoint - returns XML response to play AWOS audio."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    # Get the base URL from environment or construct it
    base_url = os.getenv("BASE_URL", "http://localhost:8000")
    audio_url = f"{base_url}/4FL5/awos.mp3"
    
    # Check if AWOS file exists
    if not weather_service.awos_file_exists():
        # Return TwiML with a fallback message
        twiml_response = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Weather information is currently unavailable. Please try again later.</Say>
</Response>"""
    else:
        # Return TwiML to play the AWOS audio
        twiml_response = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Play>{audio_url}</Play>
</Response>"""
    
    return Response(
        content=twiml_response,
        media_type="application/xml",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/4FL5/weather")
async def get_weather_data():
    """Get current weather data in JSON format."""
    global weather_service
    
    if not weather_service:
        raise HTTPException(status_code=503, detail="Weather service not initialized")
    
    weather_data = weather_service.get_current_weather_data()
    
    if not weather_data:
        raise HTTPException(status_code=404, detail="Weather data not available")
    
    return weather_data

# Recording Upload Endpoints

@app.post("/recordings/upload")
async def upload_recording(
    file: UploadFile = File(...),
    station_id: str = Form(...)
):
    """
    Upload recording file from sound-player-container to S3.

    Args:
        file: The WebM recording file
        station_id: Station ID (e.g., "4FL5_122900")
    """
    global s3_upload_manager

    # Check if S3 upload is configured
    if not s3_upload_manager:
        raise HTTPException(
            status_code=503,
            detail="Recording upload service not available - S3 not configured"
        )

    # Validate file type
    if not file.filename or not file.filename.lower().endswith('.webm'):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only WebM files are supported"
        )

    # Validate file size (max 100MB)
    max_file_size = 100 * 1024 * 1024  # 100MB
    if file.size and file.size > max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size is {max_file_size // (1024*1024)}MB"
        )

    try:
        # Create temporary file for upload
        with tempfile.NamedTemporaryFile(delete=False, suffix='.webm') as temp_file:
            # Copy uploaded file to temporary file
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = Path(temp_file.name)

        try:
            # Upload to S3 (timestamp extracted from filename or use current time)
            upload_result = s3_upload_manager.upload_recording(
                file_path=temp_file_path,
                filename=file.filename,
                station_id=station_id,
                station_timezone='UTC'  # Could be made configurable per station
            )

            logger.info(f"Recording uploaded successfully: {file.filename} for station {station_id}")

            return {
                "status": "success",
                "filename": file.filename,
                "station_id": station_id,
                "s3_key": upload_result['s3_key'],
                "bucket": upload_result['bucket'],
                "file_size": upload_result['file_size'],
                "upload_time": upload_result['upload_time']
            }

        finally:
            # Clean up temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()

    except Exception as e:
        logger.error(f"Error uploading recording: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

# Recording Retrieval Endpoints

def validate_date_format(date: str) -> bool:
    """Validate date is in YYYYMMDD format."""
    import re
    return bool(re.match(r'^\d{8}$', date))

def validate_station_name(station: str) -> bool:
    """Validate station name format."""
    import re
    return bool(re.match(r'^[A-Z0-9_]+$', station))

def validate_filename(filename: str) -> bool:
    """Validate recording filename format."""
    import re
    return bool(re.match(r'^\d{8}_\d{6}_\d{3}_d\d+\.webm$', filename))

@app.get(
    "/recordings/{station}/{date}",
    response_model=RecordingsListResponse,
    summary="List recordings for date and station",
    description="""
    List all audio recordings for a specific station and date.

    **Parameters:**
    - `station`: Station identifier (e.g., "4FL5_122900")
    - `date`: Date in YYYYMMDD format (e.g., "20250723")

    **Returns:**
    - Complete list of recordings with metadata including filename, start time, duration, and direct download URL
    - Summary statistics: total recordings, total duration, total size

    **Example:**
    ```
    GET /recordings/4FL5_122900/20250723
    ```

    **Note:** This endpoint accesses S3/R2 storage and provides download URLs through this API.
    """,
    responses={
        400: {"description": "Bad request", "model": ErrorResponse},
        404: {"description": "Not found", "model": ErrorResponse},
        500: {"description": "Internal server error", "model": ErrorResponse}
    }
)
async def list_recordings(station: str, date: str):
    """List all recordings for a given station and date."""

    # Validate input parameters
    if not validate_station_name(station):
        raise HTTPException(
            status_code=400,
            detail="Invalid station name format"
        )

    if not validate_date_format(date):
        raise HTTPException(
            status_code=400,
            detail="Invalid date format. Use YYYYMMDD format (e.g., 20250723)"
        )

    try:
        # Get base URL for download links
        base_url = os.getenv("BASE_URL", "http://localhost:8000")

        # Get recordings from service
        service = get_recordings_service()
        recordings = await service.list_recordings(station, date, base_url)

        # Calculate summary statistics
        total_recordings = len(recordings)
        total_duration_ms = sum(r.duration_ms for r in recordings)
        total_size_bytes = sum(r.size_bytes for r in recordings)

        # Return response
        return RecordingsListResponse(
            station=station,
            date=date,
            total_recordings=total_recordings,
            total_duration_ms=total_duration_ms,
            total_size_bytes=total_size_bytes,
            recordings=recordings
        )

    except HTTPException:
        # Re-raise HTTP exceptions from service
        raise
    except Exception as e:
        logger.error(f"Error listing recordings: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while fetching recordings: {str(e)}"
        )

@app.get(
    "/recordings/{station}/{date}/{filename}",
    summary="Stream/download specific recording",
    description="""
    Stream or download a specific audio recording file.

    **Parameters:**
    - `station`: Station identifier (e.g., "4FL5_122900")
    - `date`: Date in YYYYMMDD format (e.g., "20250723")
    - `filename`: Recording filename (e.g., "20250723_031946_192_d002950.webm")

    **Returns:**
    - Binary audio file in WebM format
    - Appropriate headers for streaming/download
    - CORS headers for web audio playback

    **Example:**
    ```
    GET /recordings/4FL5_122900/20250723/20250723_031946_192_d002950.webm
    ```

    **Note:** This endpoint streams the file from S3/R2 storage through the API
    to handle authentication and CORS properly for web audio playback.
    """,
    responses={
        400: {"description": "Bad request", "model": ErrorResponse},
        404: {"description": "Not found", "model": ErrorResponse},
        500: {"description": "Internal server error", "model": ErrorResponse}
    }
)
async def stream_recording(
    station: str,
    date: str,
    filename: str
):
    """Stream a specific recording file for audio playback."""

    # Validate input parameters
    if not validate_station_name(station):
        raise HTTPException(
            status_code=400,
            detail="Invalid station name format"
        )

    if not validate_date_format(date):
        raise HTTPException(
            status_code=400,
            detail="Invalid date format. Use YYYYMMDD format (e.g., 20250723)"
        )

    if not validate_filename(filename):
        raise HTTPException(
            status_code=400,
            detail="Invalid filename format"
        )

    try:
        # Get recording stream from service
        service = get_recordings_service()
        s3_response = await service.get_recording_stream(station, date, filename)

        # Create streaming response with proper headers for audio playback
        def generate():
            try:
                for chunk in s3_response['Body'].iter_chunks(chunk_size=8192):
                    yield chunk
            finally:
                s3_response['Body'].close()

        # Return streaming response with appropriate headers
        return StreamingResponse(
            generate(),
            media_type="video/webm",
            headers={
                "Content-Disposition": f"inline; filename={filename}",
                "Content-Length": str(s3_response['ContentLength']),
                "Accept-Ranges": "bytes",
                "Cache-Control": "public, max-age=3600"
                # CORS headers are handled by middleware
            }
        )

    except HTTPException:
        # Re-raise HTTP exceptions from service
        raise
    except Exception as e:
        logger.error(f"Error streaming recording: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while streaming recording: {str(e)}"
        )

# HLS Upload Endpoints

@app.post("/hls/upload")
async def upload_hls_file(
    file: UploadFile = File(...),
    file_type: str = Form(...),
    station_id: str = Form(...),
    timestamp: float = Form(...)
):
    """
    Upload HLS segment or playlist file.

    Args:
        file: The file to upload (segment .ts or playlist .m3u8)
        file_type: Type of file ('segment' or 'playlist')
        station_id: Station ID for organization
        timestamp: Upload timestamp
    """
    try:
        # Validate file type
        if file_type not in ['segment', 'playlist']:
            raise HTTPException(status_code=400, detail="Invalid file type. Must be 'segment' or 'playlist'")

        # Create station directory
        station_dir = HLS_STORAGE_PATH / station_id
        station_dir.mkdir(parents=True, exist_ok=True)

        # Determine file path
        if file_type == 'playlist':
            file_path = station_dir / "playlist.m3u8"
        else:
            # For segments, use the original filename
            file_path = station_dir / file.filename

        # Save the file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"HLS {file_type} uploaded: {file.filename} for station {station_id}")

        return {
            "status": "success",
            "file_type": file_type,
            "filename": file.filename,
            "station_id": station_id,
            "timestamp": timestamp,
            "size": file_path.stat().st_size
        }

    except Exception as e:
        logger.error(f"Error uploading HLS file: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.websocket("/hls/ws/{station_id}")
async def hls_websocket_endpoint(websocket: WebSocket, station_id: str):
    """
    WebSocket endpoint for real-time HLS file uploads.

    Protocol:
    - Client sends JSON message with file metadata
    - Client sends binary file data
    - Server responds with confirmation
    """
    await hls_manager.connect(websocket)

    try:
        # Create station directory
        station_dir = HLS_STORAGE_PATH / station_id
        station_dir.mkdir(parents=True, exist_ok=True)

        while True:
            # Receive metadata message
            metadata_text = await websocket.receive_text()
            metadata = json.loads(metadata_text)

            file_type = metadata.get('type')
            filename = metadata.get('filename')
            file_size = metadata.get('size')
            timestamp = metadata.get('timestamp', time.time())

            if not all([file_type, filename, file_size]):
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": "Missing required metadata fields"
                }))
                continue

            # Validate file type
            if file_type not in ['segment', 'playlist']:
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": "Invalid file type"
                }))
                continue

            # Determine file path
            if file_type == 'playlist':
                file_path = station_dir / "playlist.m3u8"
            else:
                file_path = station_dir / filename

            # Receive file data
            file_data = await websocket.receive_bytes()

            # Validate file size
            if len(file_data) != file_size:
                await websocket.send_text(json.dumps({
                    "status": "error",
                    "message": f"File size mismatch. Expected {file_size}, got {len(file_data)}"
                }))
                continue

            # Save file
            with open(file_path, "wb") as f:
                f.write(file_data)

            logger.info(f"HLS {file_type} received via WebSocket: {filename} for station {station_id}")

            # Send confirmation
            await websocket.send_text(json.dumps({
                "status": "success",
                "file_type": file_type,
                "filename": filename,
                "station_id": station_id,
                "timestamp": timestamp,
                "size": len(file_data)
            }))

    except WebSocketDisconnect:
        logger.info(f"HLS WebSocket disconnected for station {station_id}")
    except Exception as e:
        logger.error(f"Error in HLS WebSocket for station {station_id}: {e}")
        try:
            await websocket.send_text(json.dumps({
                "status": "error",
                "message": str(e)
            }))
        except:
            pass
    finally:
        hls_manager.disconnect(websocket)

# Removed /player endpoint - now handled by dedicated Next.js webapp

@app.get("/hls/{station_id}/playlist.m3u8")
async def serve_hls_playlist(station_id: str):
    """Serve HLS playlist for a specific station."""
    playlist_path = HLS_STORAGE_PATH / station_id / "playlist.m3u8"

    if not playlist_path.exists():
        raise HTTPException(status_code=404, detail="Playlist not found")

    return FileResponse(
        path=str(playlist_path),
        media_type="application/vnd.apple.mpegurl",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

@app.get("/hls/{station_id}/status")
async def get_hls_status(station_id: str):
    """Get HLS streaming status for a station with staleness detection."""
    import time

    station_dir = HLS_STORAGE_PATH / station_id

    if not station_dir.exists():
        return {
            "station_id": station_id,
            "status": "not_found",
            "playlist_exists": False,
            "segment_count": 0,
            "is_stale": True,
            "last_update": None,
            "seconds_since_update": None
        }

    playlist_path = station_dir / "playlist.m3u8"
    playlist_exists = playlist_path.exists()

    # Count segments
    segment_count = len(list(station_dir.glob("*.ts")))

    # Determine stream staleness
    current_time = time.time()
    last_update = None
    seconds_since_update = None
    is_stale = True

    if playlist_exists:
        last_update = playlist_path.stat().st_mtime
        seconds_since_update = current_time - last_update

        # Consider stream stale if no updates for more than 5 seconds
        # (with 1-2 second segments and 6 segments in playlist, 5 seconds is appropriate)
        is_stale = seconds_since_update > 5

    # Also check most recent segment timestamp for additional validation
    most_recent_segment_time = None
    if segment_count > 0:
        segments = list(station_dir.glob("*.ts"))
        if segments:
            # Get the most recently modified segment
            most_recent_segment = max(segments, key=lambda p: p.stat().st_mtime)
            most_recent_segment_time = most_recent_segment.stat().st_mtime

            # If segment is more recent than playlist, use segment time
            if most_recent_segment_time > (last_update or 0):
                seconds_since_update = current_time - most_recent_segment_time
                is_stale = seconds_since_update > 5

    # Determine overall status
    if not playlist_exists:
        status = "inactive"
    elif is_stale:
        status = "stale"
    else:
        status = "active"

    return {
        "station_id": station_id,
        "status": status,
        "playlist_exists": playlist_exists,
        "segment_count": segment_count,
        "is_stale": is_stale,
        "last_update": last_update,
        "seconds_since_update": seconds_since_update,
        "most_recent_segment_time": most_recent_segment_time
    }

@app.get("/hls/{station_id}/{segment_name}")
async def serve_hls_segment(station_id: str, segment_name: str):
    """Serve HLS segment for a specific station."""
    # Validate segment filename (security)
    if not segment_name.endswith('.ts') or '..' in segment_name:
        raise HTTPException(status_code=400, detail="Invalid segment name")

    segment_path = HLS_STORAGE_PATH / station_id / segment_name

    if not segment_path.exists():
        raise HTTPException(status_code=404, detail="Segment not found")

    return FileResponse(
        path=str(segment_path),
        media_type="video/mp2t",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    
    # Run the FastAPI application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
