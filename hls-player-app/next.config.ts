import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable standalone output for Docker deployment
  output: 'standalone',

  // Optimize for production
  poweredByHeader: false,

  // Skip type checking during build for Docker (will be checked in development)
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // Skip ESLint during build for Docker
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },

  // Environment variables that should be available at build time
  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_STATION_ID: process.env.NEXT_PUBLIC_STATION_ID,
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
