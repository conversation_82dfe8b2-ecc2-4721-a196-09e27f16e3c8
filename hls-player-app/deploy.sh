#!/bin/bash

# HLS Player Deployment Script
# This script builds and deploys the HLS Player application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="hls-player"
CONTAINER_NAME="hls-player-app"
PORT="3000"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Build the Docker image
build_image() {
    log_info "Building Docker image: $IMAGE_NAME"
    
    if docker build -t $IMAGE_NAME .; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Stop and remove existing container
cleanup_container() {
    if docker ps -a --format 'table {{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
        log_info "Stopping and removing existing container: $CONTAINER_NAME"
        docker stop $CONTAINER_NAME > /dev/null 2>&1 || true
        docker rm $CONTAINER_NAME > /dev/null 2>&1 || true
        log_success "Existing container removed"
    fi
}

# Run the container
run_container() {
    log_info "Starting new container: $CONTAINER_NAME"
    
    # Load environment variables from .env.local if it exists
    ENV_FILE=""
    if [ -f ".env.local" ]; then
        ENV_FILE="--env-file .env.local"
        log_info "Using environment variables from .env.local"
    else
        log_warning ".env.local not found, using default environment variables"
    fi
    
    if docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:3000 \
        $ENV_FILE \
        --restart unless-stopped \
        $IMAGE_NAME; then
        log_success "Container started successfully"
        log_info "Application is available at: http://localhost:$PORT"
    else
        log_error "Failed to start container"
        exit 1
    fi
}

# Show container logs
show_logs() {
    log_info "Container logs (last 20 lines):"
    docker logs --tail 20 $CONTAINER_NAME
}

# Health check
health_check() {
    log_info "Performing health check..."
    sleep 5  # Wait for container to start
    
    if curl -f http://localhost:$PORT/api/health > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_warning "Health check failed - container may still be starting"
    fi
}

# Main deployment process
main() {
    log_info "Starting HLS Player deployment..."
    
    check_docker
    build_image
    cleanup_container
    run_container
    show_logs
    health_check
    
    log_success "Deployment completed successfully!"
    log_info "Application URL: http://localhost:$PORT"
    log_info "Health check URL: http://localhost:$PORT/api/health"
    log_info ""
    log_info "Useful commands:"
    log_info "  View logs: docker logs -f $CONTAINER_NAME"
    log_info "  Stop container: docker stop $CONTAINER_NAME"
    log_info "  Remove container: docker rm $CONTAINER_NAME"
}

# Handle command line arguments
case "${1:-deploy}" in
    "build")
        check_docker
        build_image
        ;;
    "run")
        check_docker
        cleanup_container
        run_container
        ;;
    "logs")
        docker logs -f $CONTAINER_NAME
        ;;
    "stop")
        docker stop $CONTAINER_NAME
        log_success "Container stopped"
        ;;
    "clean")
        cleanup_container
        docker rmi $IMAGE_NAME > /dev/null 2>&1 || true
        log_success "Cleanup completed"
        ;;
    "deploy"|*)
        main
        ;;
esac
