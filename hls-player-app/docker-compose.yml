version: '3.8'

services:
  hls-player:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-https://awosnew.skytraces.com}
      - NEXT_PUBLIC_STATION_ID=${NEXT_PUBLIC_STATION_ID:-4FL5}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.hls-player.rule=Host(`app.skytraces.com`)"
      - "traefik.http.routers.hls-player.tls=true"
      - "traefik.http.routers.hls-player.tls.certresolver=letsencrypt"
      - "traefik.http.services.hls-player.loadbalancer.server.port=3000"

networks:
  default:
    external: true
    name: web
