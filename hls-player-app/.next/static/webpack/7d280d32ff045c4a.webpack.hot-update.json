{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-progress/dist/index.mjs", "(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftomsuys%2FDocuments%2FGitHub%2FSayWeather_ridge%2Fhls-player-app%2Fsrc%2Fcomponents%2Fhls-player.tsx%22%2C%22ids%22%3A%5B%22HLSPlayer%22%5D%7D&server=false!", "(app-pages-browser)/./src/components/hls-player.tsx", "(app-pages-browser)/./src/components/ui/badge.tsx", "(app-pages-browser)/./src/components/ui/progress.tsx", "(app-pages-browser)/./src/lib/config.ts"]}