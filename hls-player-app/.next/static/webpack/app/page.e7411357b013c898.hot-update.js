"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Aggressive low-latency settings\n        backBufferLength: 10,\n        maxBufferLength: 4,\n        maxMaxBufferLength: 8,\n        liveSyncDurationCount: 1,\n        liveMaxLatencyDurationCount: 2,\n        liveDurationInfinity: true,\n        // Faster loading timeouts for responsiveness\n        manifestLoadingTimeOut: 5000,\n        manifestLoadingMaxRetry: 2,\n        manifestLoadingRetryDelay: 500,\n        levelLoadingTimeOut: 5000,\n        fragLoadingTimeOut: 8000,\n        // Additional low-latency settings\n        testBandwidth: false // Skip initial bandwidth test for faster startup\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean';\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});