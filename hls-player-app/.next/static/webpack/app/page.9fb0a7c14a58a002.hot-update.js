"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Adaptive low-latency settings that balance performance and reliability\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        liveDurationInfinity: true,\n        // Balanced loading timeouts - faster than defaults but not too aggressive\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        // Additional adaptive settings\n        startLevel: -1,\n        capLevelToPlayerSize: false,\n        testBandwidth: true,\n        abrEwmaFastLive: 3.0,\n        abrEwmaSlowLive: 9.0,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4 // Max 4 seconds loading delay before retry\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBOztDQUVDLEdBc0NELHdCQUF3QjtBQUN4QixNQUFNQSxnQkFBMkI7SUFDL0JDLEtBQUs7UUFDSEMsU0FBU0MsK0JBQW9DLElBQUksQ0FBK0I7UUFDaEZHLFNBQVM7SUFDWDtJQUNBQyxRQUFRO1FBQ05DLGtCQUFrQkwsTUFBa0MsSUFBSSxDQUFNO1FBQzlETyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsa0JBQWtCO1lBQ2hCQyxRQUFRO1lBQ1JDLE9BQU8sS0FBTyxZQUFZO1FBQzVCO0lBQ0Y7SUFDQUMsS0FBSztRQUNIQyxPQUFPYixrQkFBeUI7UUFDaENjLGNBQWM7UUFDZEMsZ0JBQWdCO1FBQ2hCLHlFQUF5RTtRQUN6RUMsa0JBQWtCO1FBQ2xCQyxpQkFBaUI7UUFDakJDLG9CQUFvQjtRQUNwQkMsdUJBQXVCO1FBQ3ZCQyw2QkFBNkI7UUFDN0JDLHNCQUFzQjtRQUN0QiwwRUFBMEU7UUFDMUVDLHdCQUF3QjtRQUN4QkMseUJBQXlCO1FBQ3pCQywyQkFBMkI7UUFDM0JDLHFCQUFxQjtRQUNyQkMsb0JBQW9CO1FBQ3BCLCtCQUErQjtRQUMvQkMsWUFBWSxDQUFDO1FBQ2JDLHNCQUFzQjtRQUN0QkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGlCQUFpQjtRQUNqQkMsb0JBQW9CO1FBQ3BCQyxpQkFBaUIsRUFBYywyQ0FBMkM7SUFDNUU7SUFDQUMsSUFBSTtRQUNGQyxPQUFPO1FBQ1BDLG1CQUFtQjtJQUNyQjtBQUNGO0FBRUEsaUNBQWlDO0FBQ2pDLE1BQU1DLHVCQUF1QjtJQUMzQixNQUFNcEMsTUFxRCtCO0lBbkRyQyxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO2dCQUNMVyxLQUFLO29CQUNILEdBQUdmLGNBQWNlLEdBQUc7b0JBQ3BCQyxPQUFPO2dCQUNUO2dCQUNBcUIsSUFBSTtvQkFDRixHQUFHckMsY0FBY3FDLEVBQUU7b0JBQ25CRSxtQkFBbUI7Z0JBQ3JCO1lBQ0Y7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTHhCLEtBQUs7b0JBQ0gsR0FBR2YsY0FBY2UsR0FBRztvQkFDcEJDLE9BQU87Z0JBQ1Q7Z0JBQ0FxQixJQUFJO29CQUNGLEdBQUdyQyxjQUFjcUMsRUFBRTtvQkFDbkJFLG1CQUFtQjtnQkFDckI7WUFDRjtRQUVGO1lBQ0UsT0FBTyxDQUFDO0lBQ1o7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixNQUFNRSxTQUFvQjtJQUMvQixHQUFHekMsYUFBYTtJQUNoQixHQUFHd0Msc0JBQXNCO0FBQzNCLEVBQUM7QUFFRCxtQkFBbUI7QUFDWixNQUFNRSxZQUFZLENBQUNDO0lBQ3hCLE1BQU16QyxVQUFVdUMsT0FBT3hDLEdBQUcsQ0FBQ0MsT0FBTyxDQUFDMEMsT0FBTyxDQUFDLE9BQU8sSUFBSSx3QkFBd0I7O0lBQzlFLE1BQU1DLGdCQUFnQkYsU0FBU0csVUFBVSxDQUFDLE9BQU9ILFdBQVcsSUFBYSxPQUFUQTtJQUNoRSxPQUFPLEdBQWFFLE9BQVYzQyxTQUF3QixPQUFkMkM7QUFDdEIsRUFBQztBQUVNLE1BQU1FLG9CQUFvQixDQUFDQztJQUNoQyxPQUFPTixVQUFVLFFBQWtCLE9BQVZNLFdBQVU7QUFDckMsRUFBQztBQUVNLE1BQU1DLGtCQUFrQixDQUFDRDtJQUM5QixPQUFPTixVQUFVLFFBQWtCLE9BQVZNLFdBQVU7QUFDckMsRUFBQztBQUVNLE1BQU1FLG1CQUFtQixDQUFDRixXQUFtQkc7SUFDbEQsT0FBT1QsVUFBVSxRQUFxQlMsT0FBYkgsV0FBVSxLQUFlLE9BQVpHO0FBQ3hDLEVBQUM7QUFFRCx1QkFBdUI7QUFDaEIsTUFBTUMsb0JBQW9CLENBQUNKO0lBQ2hDLE9BQU8sZUFBZUssSUFBSSxDQUFDTDtBQUM3QixFQUFDO0FBRU0sTUFBTU0saUJBQWlCLENBQUNDO0lBQzdCLElBQUk7UUFDRixJQUFJQyxJQUFJRDtRQUNSLE9BQU87SUFDVCxFQUFFLFVBQU07UUFDTixPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsY0FBYztBQUNQLE1BQU1FLGdCQUFnQixDQUFDaEI7SUFDNUIsSUFBSSxDQUFDQSxVQUFVLE9BQU9BLFdBQVcsVUFBVSxPQUFPO0lBRWxELE1BQU1pQixNQUFNakI7SUFFWixPQUFPLENBQUMsQ0FDTmlCLENBQUFBLElBQUl6RCxHQUFHLElBQ1AsT0FBT3lELElBQUl6RCxHQUFHLEtBQUssWUFDbkJ5RCxJQUFJekQsR0FBRyxLQUFLLFFBQ1osT0FBTyxJQUFLQSxHQUFHLENBQTZCQyxPQUFPLEtBQUssWUFDeEQsT0FBTyxJQUFLRCxHQUFHLENBQTZCSyxPQUFPLEtBQUssWUFDeERvRCxJQUFJbkQsTUFBTSxJQUNWLE9BQU9tRCxJQUFJbkQsTUFBTSxLQUFLLFlBQ3RCbUQsSUFBSW5ELE1BQU0sS0FBSyxRQUNmLE9BQU8sSUFBS0EsTUFBTSxDQUE2QkMsZ0JBQWdCLEtBQUssWUFDcEUsT0FBTyxJQUFLRCxNQUFNLENBQTZCRyxRQUFRLEtBQUssYUFDNURnRCxJQUFJM0MsR0FBRyxJQUNQLE9BQU8yQyxJQUFJM0MsR0FBRyxLQUFLLFlBQ25CMkMsSUFBSTNDLEdBQUcsS0FBSyxRQUNaLE9BQU8sSUFBS0EsR0FBRyxDQUE2QkMsS0FBSyxLQUFLLFNBQVE7QUFFbEUsRUFBQztBQUVELGlFQUFleUIsTUFBTUEsRUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL3RvbXN1eXMvRG9jdW1lbnRzL0dpdEh1Yi9TYXlXZWF0aGVyX3JpZGdlL2hscy1wbGF5ZXItYXBwL3NyYy9saWIvY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29uZmlndXJhdGlvbiBmb3IgdGhlIEhMUyBQbGF5ZXIgQXBwbGljYXRpb25cbiAqL1xuXG5leHBvcnQgaW50ZXJmYWNlIEFwcENvbmZpZyB7XG4gIGFwaToge1xuICAgIGJhc2VVcmw6IHN0cmluZ1xuICAgIHRpbWVvdXQ6IG51bWJlclxuICB9XG4gIHBsYXllcjoge1xuICAgIGRlZmF1bHRTdGF0aW9uSWQ6IHN0cmluZ1xuICAgIGF1dG9QbGF5OiBib29sZWFuXG4gICAgYXV0b1JlZnJlc2g6IGJvb2xlYW5cbiAgICByZWZyZXNoSW50ZXJ2YWxzOiB7XG4gICAgICBzdGF0dXM6IG51bWJlclxuICAgICAgc3RhdHM6IG51bWJlclxuICAgIH1cbiAgfVxuICBobHM6IHtcbiAgICBkZWJ1ZzogYm9vbGVhblxuICAgIGVuYWJsZVdvcmtlcjogYm9vbGVhblxuICAgIGxvd0xhdGVuY3lNb2RlOiBib29sZWFuXG4gICAgYmFja0J1ZmZlckxlbmd0aDogbnVtYmVyXG4gICAgbWF4QnVmZmVyTGVuZ3RoOiBudW1iZXJcbiAgICBtYXhNYXhCdWZmZXJMZW5ndGg6IG51bWJlclxuICAgIGxpdmVTeW5jRHVyYXRpb25Db3VudDogbnVtYmVyXG4gICAgbGl2ZU1heExhdGVuY3lEdXJhdGlvbkNvdW50OiBudW1iZXJcbiAgICBsaXZlRHVyYXRpb25JbmZpbml0eTogYm9vbGVhblxuICAgIG1hbmlmZXN0TG9hZGluZ1RpbWVPdXQ6IG51bWJlclxuICAgIG1hbmlmZXN0TG9hZGluZ01heFJldHJ5OiBudW1iZXJcbiAgICBtYW5pZmVzdExvYWRpbmdSZXRyeURlbGF5OiBudW1iZXJcbiAgICBsZXZlbExvYWRpbmdUaW1lT3V0OiBudW1iZXJcbiAgICBmcmFnTG9hZGluZ1RpbWVPdXQ6IG51bWJlclxuICB9XG4gIHVpOiB7XG4gICAgdGhlbWU6ICdsaWdodCcgfCAnZGFyaycgfCAnc3lzdGVtJ1xuICAgIHNob3dBZHZhbmNlZFN0YXRzOiBib29sZWFuXG4gIH1cbn1cblxuLy8gRGVmYXVsdCBjb25maWd1cmF0aW9uXG5jb25zdCBkZWZhdWx0Q29uZmlnOiBBcHBDb25maWcgPSB7XG4gIGFwaToge1xuICAgIGJhc2VVcmw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCB8fCAnaHR0cHM6Ly9hd29zbmV3LnNreXRyYWNlcy5jb20nLFxuICAgIHRpbWVvdXQ6IDEwMDAwXG4gIH0sXG4gIHBsYXllcjoge1xuICAgIGRlZmF1bHRTdGF0aW9uSWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NUQVRJT05fSUQgfHwgJzRGTDUnLFxuICAgIGF1dG9QbGF5OiBmYWxzZSxcbiAgICBhdXRvUmVmcmVzaDogdHJ1ZSxcbiAgICByZWZyZXNoSW50ZXJ2YWxzOiB7XG4gICAgICBzdGF0dXM6IDUwMDAsIC8vIDUgc2Vjb25kc1xuICAgICAgc3RhdHM6IDIwMDAgICAvLyAyIHNlY29uZHNcbiAgICB9XG4gIH0sXG4gIGhsczoge1xuICAgIGRlYnVnOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyxcbiAgICBlbmFibGVXb3JrZXI6IHRydWUsXG4gICAgbG93TGF0ZW5jeU1vZGU6IHRydWUsXG4gICAgLy8gQWRhcHRpdmUgbG93LWxhdGVuY3kgc2V0dGluZ3MgdGhhdCBiYWxhbmNlIHBlcmZvcm1hbmNlIGFuZCByZWxpYWJpbGl0eVxuICAgIGJhY2tCdWZmZXJMZW5ndGg6IDgsICAgICAgICAgLy8gTW9kZXJhdGUgYnVmZmVyIGZvciBzdGFiaWxpdHlcbiAgICBtYXhCdWZmZXJMZW5ndGg6IDYsICAgICAgICAgIC8vIDYgc2Vjb25kcyAtIGJhbGFuY2UgYmV0d2VlbiBsYXRlbmN5IGFuZCByZWxpYWJpbGl0eVxuICAgIG1heE1heEJ1ZmZlckxlbmd0aDogMTIsICAgICAgLy8gMTIgc2Vjb25kcyBtYXggLSBhbGxvd3MgcmVjb3ZlcnkgZnJvbSBuZXR3b3JrIGlzc3Vlc1xuICAgIGxpdmVTeW5jRHVyYXRpb25Db3VudDogMiwgICAgLy8gU3RhcnQgMiBzZWdtZW50cyBiZWhpbmQgbGl2ZSBlZGdlIChzYWZlciB0aGFuIDEpXG4gICAgbGl2ZU1heExhdGVuY3lEdXJhdGlvbkNvdW50OiA0LCAvLyBBbGxvdyB1cCB0byA0IHNlZ21lbnRzIGJlaGluZCBiZWZvcmUgcmVidWZmZXJpbmdcbiAgICBsaXZlRHVyYXRpb25JbmZpbml0eTogdHJ1ZSxcbiAgICAvLyBCYWxhbmNlZCBsb2FkaW5nIHRpbWVvdXRzIC0gZmFzdGVyIHRoYW4gZGVmYXVsdHMgYnV0IG5vdCB0b28gYWdncmVzc2l2ZVxuICAgIG1hbmlmZXN0TG9hZGluZ1RpbWVPdXQ6IDgwMDAsICAgLy8gOCBzZWNvbmRzIC0gYmFsYW5jZSBiZXR3ZWVuIHNwZWVkIGFuZCByZWxpYWJpbGl0eVxuICAgIG1hbmlmZXN0TG9hZGluZ01heFJldHJ5OiAzLCAgICAgLy8gS2VlcCAzIHJldHJpZXMgZm9yIHJlbGlhYmlsaXR5XG4gICAgbWFuaWZlc3RMb2FkaW5nUmV0cnlEZWxheTogNzUwLCAvLyA3NTBtcyAtIGZhc3RlciB0aGFuIGRlZmF1bHQgYnV0IG5vdCB0b28gYWdncmVzc2l2ZVxuICAgIGxldmVsTG9hZGluZ1RpbWVPdXQ6IDgwMDAsICAgICAgLy8gOCBzZWNvbmRzIGZvciBsZXZlbCBsb2FkaW5nXG4gICAgZnJhZ0xvYWRpbmdUaW1lT3V0OiAxMDAwMCwgICAgICAvLyAxMCBzZWNvbmRzIGZvciBmcmFnbWVudCBsb2FkaW5nXG4gICAgLy8gQWRkaXRpb25hbCBhZGFwdGl2ZSBzZXR0aW5nc1xuICAgIHN0YXJ0TGV2ZWw6IC0xLCAgICAgICAgICAgICAgICAgLy8gQXV0by1zZWxlY3QgaW5pdGlhbCBxdWFsaXR5XG4gICAgY2FwTGV2ZWxUb1BsYXllclNpemU6IGZhbHNlLCAgICAvLyBEb24ndCBsaW1pdCBxdWFsaXR5IHRvIHBsYXllciBzaXplXG4gICAgdGVzdEJhbmR3aWR0aDogdHJ1ZSwgICAgICAgICAgICAvLyBUZXN0IGJhbmR3aWR0aCBmb3IgYWRhcHRpdmUgc3RyZWFtaW5nXG4gICAgYWJyRXdtYUZhc3RMaXZlOiAzLjAsICAgICAgICAgIC8vIEZhc3RlciBhZGFwdGF0aW9uIGZvciBsaXZlIHN0cmVhbXNcbiAgICBhYnJFd21hU2xvd0xpdmU6IDkuMCwgICAgICAgICAgLy8gU2xvd2VyIGFkYXB0YXRpb24gdG8gYXZvaWQgb3NjaWxsYXRpb25cbiAgICBtYXhTdGFydmF0aW9uRGVsYXk6IDQsICAgICAgICAgLy8gTWF4IDQgc2Vjb25kcyBzdGFydmF0aW9uIGJlZm9yZSBxdWFsaXR5IGRyb3BcbiAgICBtYXhMb2FkaW5nRGVsYXk6IDQgICAgICAgICAgICAgLy8gTWF4IDQgc2Vjb25kcyBsb2FkaW5nIGRlbGF5IGJlZm9yZSByZXRyeVxuICB9LFxuICB1aToge1xuICAgIHRoZW1lOiAnc3lzdGVtJyxcbiAgICBzaG93QWR2YW5jZWRTdGF0czogZmFsc2VcbiAgfVxufVxuXG4vLyBFbnZpcm9ubWVudC1zcGVjaWZpYyBvdmVycmlkZXNcbmNvbnN0IGdldEVudmlyb25tZW50Q29uZmlnID0gKCk6IFBhcnRpYWw8QXBwQ29uZmlnPiA9PiB7XG4gIGNvbnN0IGVudiA9IHByb2Nlc3MuZW52Lk5PREVfRU5WXG5cbiAgc3dpdGNoIChlbnYpIHtcbiAgICBjYXNlICdkZXZlbG9wbWVudCc6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBobHM6IHtcbiAgICAgICAgICAuLi5kZWZhdWx0Q29uZmlnLmhscyxcbiAgICAgICAgICBkZWJ1ZzogdHJ1ZVxuICAgICAgICB9LFxuICAgICAgICB1aToge1xuICAgICAgICAgIC4uLmRlZmF1bHRDb25maWcudWksXG4gICAgICAgICAgc2hvd0FkdmFuY2VkU3RhdHM6IHRydWVcbiAgICAgICAgfVxuICAgICAgfVxuICAgIFxuICAgIGNhc2UgJ3Byb2R1Y3Rpb24nOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaGxzOiB7XG4gICAgICAgICAgLi4uZGVmYXVsdENvbmZpZy5obHMsXG4gICAgICAgICAgZGVidWc6IGZhbHNlXG4gICAgICAgIH0sXG4gICAgICAgIHVpOiB7XG4gICAgICAgICAgLi4uZGVmYXVsdENvbmZpZy51aSxcbiAgICAgICAgICBzaG93QWR2YW5jZWRTdGF0czogZmFsc2VcbiAgICAgICAgfVxuICAgICAgfVxuICAgIFxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4ge31cbiAgfVxufVxuXG4vLyBNZXJnZSBjb25maWd1cmF0aW9uc1xuZXhwb3J0IGNvbnN0IGNvbmZpZzogQXBwQ29uZmlnID0ge1xuICAuLi5kZWZhdWx0Q29uZmlnLFxuICAuLi5nZXRFbnZpcm9ubWVudENvbmZpZygpXG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBnZXRBcGlVcmwgPSAoZW5kcG9pbnQ6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IGJhc2VVcmwgPSBjb25maWcuYXBpLmJhc2VVcmwucmVwbGFjZSgvXFwvJC8sICcnKSAvLyBSZW1vdmUgdHJhaWxpbmcgc2xhc2hcbiAgY29uc3QgY2xlYW5FbmRwb2ludCA9IGVuZHBvaW50LnN0YXJ0c1dpdGgoJy8nKSA/IGVuZHBvaW50IDogYC8ke2VuZHBvaW50fWBcbiAgcmV0dXJuIGAke2Jhc2VVcmx9JHtjbGVhbkVuZHBvaW50fWBcbn1cblxuZXhwb3J0IGNvbnN0IGdldEhMU1BsYXlsaXN0VXJsID0gKHN0YXRpb25JZDogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIGdldEFwaVVybChgL2hscy8ke3N0YXRpb25JZH0vcGxheWxpc3QubTN1OGApXG59XG5cbmV4cG9ydCBjb25zdCBnZXRITFNTdGF0dXNVcmwgPSAoc3RhdGlvbklkOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gZ2V0QXBpVXJsKGAvaGxzLyR7c3RhdGlvbklkfS9zdGF0dXNgKVxufVxuXG5leHBvcnQgY29uc3QgZ2V0SExTU2VnbWVudFVybCA9IChzdGF0aW9uSWQ6IHN0cmluZywgc2VnbWVudE5hbWU6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBnZXRBcGlVcmwoYC9obHMvJHtzdGF0aW9uSWR9LyR7c2VnbWVudE5hbWV9YClcbn1cblxuLy8gVmFsaWRhdGlvbiBmdW5jdGlvbnNcbmV4cG9ydCBjb25zdCB2YWxpZGF0ZVN0YXRpb25JZCA9IChzdGF0aW9uSWQ6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICByZXR1cm4gL15bQS1aMC05X10rJC8udGVzdChzdGF0aW9uSWQpXG59XG5cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUFwaVVybCA9ICh1cmw6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICB0cnkge1xuICAgIG5ldyBVUkwodXJsKVxuICAgIHJldHVybiB0cnVlXG4gIH0gY2F0Y2gge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8vIFR5cGUgZ3VhcmRzXG5leHBvcnQgY29uc3QgaXNWYWxpZENvbmZpZyA9IChjb25maWc6IHVua25vd24pOiBjb25maWcgaXMgQXBwQ29uZmlnID0+IHtcbiAgaWYgKCFjb25maWcgfHwgdHlwZW9mIGNvbmZpZyAhPT0gJ29iamVjdCcpIHJldHVybiBmYWxzZVxuXG4gIGNvbnN0IGNmZyA9IGNvbmZpZyBhcyBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPlxuXG4gIHJldHVybiAhIShcbiAgICBjZmcuYXBpICYmXG4gICAgdHlwZW9mIGNmZy5hcGkgPT09ICdvYmplY3QnICYmXG4gICAgY2ZnLmFwaSAhPT0gbnVsbCAmJlxuICAgIHR5cGVvZiAoY2ZnLmFwaSBhcyBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPikuYmFzZVVybCA9PT0gJ3N0cmluZycgJiZcbiAgICB0eXBlb2YgKGNmZy5hcGkgYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj4pLnRpbWVvdXQgPT09ICdudW1iZXInICYmXG4gICAgY2ZnLnBsYXllciAmJlxuICAgIHR5cGVvZiBjZmcucGxheWVyID09PSAnb2JqZWN0JyAmJlxuICAgIGNmZy5wbGF5ZXIgIT09IG51bGwgJiZcbiAgICB0eXBlb2YgKGNmZy5wbGF5ZXIgYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj4pLmRlZmF1bHRTdGF0aW9uSWQgPT09ICdzdHJpbmcnICYmXG4gICAgdHlwZW9mIChjZmcucGxheWVyIGFzIFJlY29yZDxzdHJpbmcsIHVua25vd24+KS5hdXRvUGxheSA9PT0gJ2Jvb2xlYW4nICYmXG4gICAgY2ZnLmhscyAmJlxuICAgIHR5cGVvZiBjZmcuaGxzID09PSAnb2JqZWN0JyAmJlxuICAgIGNmZy5obHMgIT09IG51bGwgJiZcbiAgICB0eXBlb2YgKGNmZy5obHMgYXMgUmVjb3JkPHN0cmluZywgdW5rbm93bj4pLmRlYnVnID09PSAnYm9vbGVhbidcbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBjb25maWdcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Q29uZmlnIiwiYXBpIiwiYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwiLCJ0aW1lb3V0IiwicGxheWVyIiwiZGVmYXVsdFN0YXRpb25JZCIsIk5FWFRfUFVCTElDX1NUQVRJT05fSUQiLCJhdXRvUGxheSIsImF1dG9SZWZyZXNoIiwicmVmcmVzaEludGVydmFscyIsInN0YXR1cyIsInN0YXRzIiwiaGxzIiwiZGVidWciLCJlbmFibGVXb3JrZXIiLCJsb3dMYXRlbmN5TW9kZSIsImJhY2tCdWZmZXJMZW5ndGgiLCJtYXhCdWZmZXJMZW5ndGgiLCJtYXhNYXhCdWZmZXJMZW5ndGgiLCJsaXZlU3luY0R1cmF0aW9uQ291bnQiLCJsaXZlTWF4TGF0ZW5jeUR1cmF0aW9uQ291bnQiLCJsaXZlRHVyYXRpb25JbmZpbml0eSIsIm1hbmlmZXN0TG9hZGluZ1RpbWVPdXQiLCJtYW5pZmVzdExvYWRpbmdNYXhSZXRyeSIsIm1hbmlmZXN0TG9hZGluZ1JldHJ5RGVsYXkiLCJsZXZlbExvYWRpbmdUaW1lT3V0IiwiZnJhZ0xvYWRpbmdUaW1lT3V0Iiwic3RhcnRMZXZlbCIsImNhcExldmVsVG9QbGF5ZXJTaXplIiwidGVzdEJhbmR3aWR0aCIsImFickV3bWFGYXN0TGl2ZSIsImFickV3bWFTbG93TGl2ZSIsIm1heFN0YXJ2YXRpb25EZWxheSIsIm1heExvYWRpbmdEZWxheSIsInVpIiwidGhlbWUiLCJzaG93QWR2YW5jZWRTdGF0cyIsImdldEVudmlyb25tZW50Q29uZmlnIiwiY29uZmlnIiwiZ2V0QXBpVXJsIiwiZW5kcG9pbnQiLCJyZXBsYWNlIiwiY2xlYW5FbmRwb2ludCIsInN0YXJ0c1dpdGgiLCJnZXRITFNQbGF5bGlzdFVybCIsInN0YXRpb25JZCIsImdldEhMU1N0YXR1c1VybCIsImdldEhMU1NlZ21lbnRVcmwiLCJzZWdtZW50TmFtZSIsInZhbGlkYXRlU3RhdGlvbklkIiwidGVzdCIsInZhbGlkYXRlQXBpVXJsIiwidXJsIiwiVVJMIiwiaXNWYWxpZENvbmZpZyIsImNmZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});