"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkQuality: () => (/* binding */ NetworkQuality),\n/* harmony export */   adaptiveHLSConfigs: () => (/* binding */ adaptiveHLSConfigs),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   detectNetworkQuality: () => (/* binding */ detectNetworkQuality),\n/* harmony export */   detectNetworkQualityFromAPI: () => (/* binding */ detectNetworkQualityFromAPI),\n/* harmony export */   getAdaptiveHLSConfig: () => (/* binding */ getAdaptiveHLSConfig),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Adaptive low-latency settings that balance performance and reliability\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        liveDurationInfinity: true,\n        // Balanced loading timeouts - faster than defaults but not too aggressive\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        // Additional adaptive settings\n        startLevel: -1,\n        capLevelToPlayerSize: false,\n        testBandwidth: true,\n        abrEwmaFastLive: 3.0,\n        abrEwmaSlowLive: 9.0,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4 // Max 4 seconds loading delay before retry\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n/**\n * Network quality levels for adaptive HLS configuration\n */ var NetworkQuality = /*#__PURE__*/ function(NetworkQuality) {\n    NetworkQuality[\"EXCELLENT\"] = \"excellent\";\n    NetworkQuality[\"GOOD\"] = \"good\";\n    NetworkQuality[\"FAIR\"] = \"fair\";\n    NetworkQuality[\"POOR\"] = \"poor\";\n    return NetworkQuality;\n}({});\n/**\n * Adaptive HLS configurations based on network quality\n */ const adaptiveHLSConfigs = {\n    [\"excellent\"]: {\n        // Ultra-low latency for excellent connections\n        backBufferLength: 4,\n        maxBufferLength: 3,\n        maxMaxBufferLength: 6,\n        liveSyncDurationCount: 1,\n        liveMaxLatencyDurationCount: 2,\n        manifestLoadingTimeOut: 4000,\n        manifestLoadingMaxRetry: 2,\n        manifestLoadingRetryDelay: 300,\n        levelLoadingTimeOut: 4000,\n        fragLoadingTimeOut: 6000,\n        maxStarvationDelay: 2,\n        maxLoadingDelay: 2\n    },\n    [\"good\"]: {\n        // Balanced settings (default)\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4\n    },\n    [\"fair\"]: {\n        // More conservative for slower connections\n        backBufferLength: 15,\n        maxBufferLength: 12,\n        maxMaxBufferLength: 20,\n        liveSyncDurationCount: 3,\n        liveMaxLatencyDurationCount: 6,\n        manifestLoadingTimeOut: 12000,\n        manifestLoadingMaxRetry: 4,\n        manifestLoadingRetryDelay: 1000,\n        levelLoadingTimeOut: 12000,\n        fragLoadingTimeOut: 15000,\n        maxStarvationDelay: 6,\n        maxLoadingDelay: 6\n    },\n    [\"poor\"]: {\n        // Maximum reliability for poor connections\n        backBufferLength: 30,\n        maxBufferLength: 20,\n        maxMaxBufferLength: 40,\n        liveSyncDurationCount: 5,\n        liveMaxLatencyDurationCount: 8,\n        manifestLoadingTimeOut: 20000,\n        manifestLoadingMaxRetry: 5,\n        manifestLoadingRetryDelay: 2000,\n        levelLoadingTimeOut: 20000,\n        fragLoadingTimeOut: 25000,\n        maxStarvationDelay: 10,\n        maxLoadingDelay: 10\n    }\n};\n/**\n * Get adaptive HLS configuration based on network quality\n */ const getAdaptiveHLSConfig = function() {\n    let networkQuality = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"good\";\n    const baseConfig = config.hls;\n    const adaptiveSettings = adaptiveHLSConfigs[networkQuality];\n    return {\n        ...baseConfig,\n        ...adaptiveSettings\n    };\n};\n/**\n * Detect network quality based on actual API response times and reliability\n */ const detectNetworkQualityFromAPI = async (statusUrl)=>{\n    try {\n        const startTime = performance.now();\n        const response = await fetch(statusUrl, {\n            method: 'GET',\n            cache: 'no-cache'\n        });\n        const endTime = performance.now();\n        const responseTime = endTime - startTime;\n        if (!response.ok) {\n            return \"poor\";\n        }\n        // Classify based on API response time\n        if (responseTime < 100) {\n            return \"excellent\"; // < 100ms\n        } else if (responseTime < 300) {\n            return \"good\"; // 100-300ms\n        } else if (responseTime < 800) {\n            return \"fair\"; // 300-800ms\n        } else {\n            return \"poor\"; // > 800ms\n        }\n    } catch (error) {\n        console.warn('Failed to detect network quality from API:', error);\n        return \"poor\";\n    }\n};\n/**\n * Simple fallback network quality detection\n */ const detectNetworkQuality = ()=>{\n    // Default to good quality - will be overridden by API-based detection\n    return \"good\";\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});