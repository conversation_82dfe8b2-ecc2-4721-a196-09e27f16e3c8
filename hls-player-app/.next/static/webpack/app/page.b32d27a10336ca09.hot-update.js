"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Network quality tracking\n    const [networkQuality, setNetworkQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD);\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Detect network quality and get adaptive config\n                const detectedQuality = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.detectNetworkQuality)();\n                setNetworkQuality(detectedQuality);\n                const adaptiveConfig = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getAdaptiveHLSConfig)(detectedQuality);\n                console.log(\"Using \".concat(detectedQuality, \" network quality HLS config:\"), adaptiveConfig);\n                // Create new HLS instance with adaptive config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](adaptiveConfig);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 308,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Network\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.EXCELLENT ? \"bg-green-100 text-green-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD ? \"bg-blue-100 text-blue-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.FAIR ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\",\n                                        children: networkQuality.charAt(0).toUpperCase() + networkQuality.slice(1)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"HLgDL0HZQ693Osybo6adLzqAT5I=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});