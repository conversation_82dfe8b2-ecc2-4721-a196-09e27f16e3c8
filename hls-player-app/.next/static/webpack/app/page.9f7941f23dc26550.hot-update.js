"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Network quality tracking\n    const [networkQuality, setNetworkQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD);\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Detect network quality and get adaptive config\n                const detectedQuality = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.detectNetworkQuality)();\n                setNetworkQuality(detectedQuality);\n                const adaptiveConfig = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getAdaptiveHLSConfig)(detectedQuality);\n                console.log(\"Using \".concat(detectedQuality, \" network quality HLS config:\"), adaptiveConfig);\n                // Create new HLS instance with adaptive config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](adaptiveConfig);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 308,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            playerStats.latency > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: seekToLive,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                title: \"High latency: \".concat(playerStats.latency.toFixed(1), \"s - Click to seek to live\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Zap, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Network\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.EXCELLENT ? \"bg-green-100 text-green-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD ? \"bg-blue-100 text-blue-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.FAIR ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\",\n                                        children: networkQuality.charAt(0).toUpperCase() + networkQuality.slice(1)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"HLgDL0HZQ693Osybo6adLzqAT5I=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});