"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Balanced low-latency settings optimized for aviation radio\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        liveDurationInfinity: true,\n        // Balanced loading timeouts - responsive but not too aggressive\n        manifestLoadingTimeOut: 6000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 6000,\n        fragLoadingTimeOut: 10000,\n        // Additional reliability settings\n        startFragPrefetch: true,\n        testBandwidth: false,\n        abrEwmaFastLive: 3.0,\n        abrEwmaSlowLive: 9.0,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4 // Max loading delay before error\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});