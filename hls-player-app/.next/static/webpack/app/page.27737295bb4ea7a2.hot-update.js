"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Network quality tracking\n    const [networkQuality, setNetworkQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD);\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Detect network quality and get adaptive config\n                const networkQuality = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.detectNetworkQuality)();\n                const adaptiveConfig = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getAdaptiveHLSConfig)(networkQuality);\n                console.log(\"Using \".concat(networkQuality, \" network quality HLS config:\"), adaptiveConfig);\n                // Create new HLS instance with adaptive config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](adaptiveConfig);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Seek to live edge (reduce latency)\n    const seekToLive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[seekToLive]\": ()=>{\n            if (!hlsRef.current || !audioRef.current) return;\n            const hls = hlsRef.current;\n            if (hls.liveSyncPosition !== undefined) {\n                // Seek to near the live edge\n                const targetTime = hls.liveSyncPosition - 1 // 1 second from live edge\n                ;\n                audioRef.current.currentTime = Math.max(0, targetTime);\n            }\n        }\n    }[\"HLSPlayer.useCallback[seekToLive]\"], []);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 317,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, undefined),\n                            playerStats.latency > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: seekToLive,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                title: \"High latency: \".concat(playerStats.latency.toFixed(1), \"s - Click to seek to live\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 332,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"tMgdP6gnjqeWJfwhGnvnX/hjXR0=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});