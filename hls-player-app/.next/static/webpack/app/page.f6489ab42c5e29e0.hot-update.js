"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Adaptive low-latency settings that balance performance and reliability\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        liveDurationInfinity: true,\n        // Balanced loading timeouts - faster than defaults but not too aggressive\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        // Additional adaptive settings\n        startLevel: -1,\n        capLevelToPlayerSize: false,\n        testBandwidth: true,\n        abrEwmaFastLive: 3.0,\n        abrEwmaSlowLive: 9.0,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4 // Max 4 seconds loading delay before retry\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});