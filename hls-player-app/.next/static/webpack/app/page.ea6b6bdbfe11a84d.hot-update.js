"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Create new HLS instance with optimized config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.hls);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 288,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"qmP3Vy7oVwS6y4i7ZNSISZk6+bo=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});