"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Aggressive low-latency settings\n        backBufferLength: 10,\n        maxBufferLength: 4,\n        maxMaxBufferLength: 8,\n        liveSyncDurationCount: 1,\n        liveMaxLatencyDurationCount: 2,\n        liveDurationInfinity: true,\n        // Faster loading timeouts for responsiveness\n        manifestLoadingTimeOut: 5000,\n        manifestLoadingMaxRetry: 2,\n        manifestLoadingRetryDelay: 500,\n        levelLoadingTimeOut: 5000,\n        fragLoadingTimeOut: 8000 // Reduced from 20000\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});