"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkQuality: () => (/* binding */ NetworkQuality),\n/* harmony export */   adaptiveHLSConfigs: () => (/* binding */ adaptiveHLSConfigs),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   detectNetworkQuality: () => (/* binding */ detectNetworkQuality),\n/* harmony export */   getAdaptiveHLSConfig: () => (/* binding */ getAdaptiveHLSConfig),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Adaptive low-latency settings that balance performance and reliability\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        liveDurationInfinity: true,\n        // Balanced loading timeouts - faster than defaults but not too aggressive\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        // Additional adaptive settings\n        startLevel: -1,\n        capLevelToPlayerSize: false,\n        testBandwidth: true,\n        abrEwmaFastLive: 3.0,\n        abrEwmaSlowLive: 9.0,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4 // Max 4 seconds loading delay before retry\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n/**\n * Network quality levels for adaptive HLS configuration\n */ var NetworkQuality = /*#__PURE__*/ function(NetworkQuality) {\n    NetworkQuality[\"EXCELLENT\"] = \"excellent\";\n    NetworkQuality[\"GOOD\"] = \"good\";\n    NetworkQuality[\"FAIR\"] = \"fair\";\n    NetworkQuality[\"POOR\"] = \"poor\";\n    return NetworkQuality;\n}({});\n/**\n * Adaptive HLS configurations based on network quality\n */ const adaptiveHLSConfigs = {\n    [\"excellent\"]: {\n        // Ultra-low latency for excellent connections\n        backBufferLength: 4,\n        maxBufferLength: 3,\n        maxMaxBufferLength: 6,\n        liveSyncDurationCount: 1,\n        liveMaxLatencyDurationCount: 2,\n        manifestLoadingTimeOut: 4000,\n        manifestLoadingMaxRetry: 2,\n        manifestLoadingRetryDelay: 300,\n        levelLoadingTimeOut: 4000,\n        fragLoadingTimeOut: 6000,\n        maxStarvationDelay: 2,\n        maxLoadingDelay: 2\n    },\n    [\"good\"]: {\n        // Balanced settings (default)\n        backBufferLength: 8,\n        maxBufferLength: 6,\n        maxMaxBufferLength: 12,\n        liveSyncDurationCount: 2,\n        liveMaxLatencyDurationCount: 4,\n        manifestLoadingTimeOut: 8000,\n        manifestLoadingMaxRetry: 3,\n        manifestLoadingRetryDelay: 750,\n        levelLoadingTimeOut: 8000,\n        fragLoadingTimeOut: 10000,\n        maxStarvationDelay: 4,\n        maxLoadingDelay: 4\n    },\n    [\"fair\"]: {\n        // More conservative for slower connections\n        backBufferLength: 15,\n        maxBufferLength: 12,\n        maxMaxBufferLength: 20,\n        liveSyncDurationCount: 3,\n        liveMaxLatencyDurationCount: 6,\n        manifestLoadingTimeOut: 12000,\n        manifestLoadingMaxRetry: 4,\n        manifestLoadingRetryDelay: 1000,\n        levelLoadingTimeOut: 12000,\n        fragLoadingTimeOut: 15000,\n        maxStarvationDelay: 6,\n        maxLoadingDelay: 6\n    },\n    [\"poor\"]: {\n        // Maximum reliability for poor connections\n        backBufferLength: 30,\n        maxBufferLength: 20,\n        maxMaxBufferLength: 40,\n        liveSyncDurationCount: 5,\n        liveMaxLatencyDurationCount: 8,\n        manifestLoadingTimeOut: 20000,\n        manifestLoadingMaxRetry: 5,\n        manifestLoadingRetryDelay: 2000,\n        levelLoadingTimeOut: 20000,\n        fragLoadingTimeOut: 25000,\n        maxStarvationDelay: 10,\n        maxLoadingDelay: 10\n    }\n};\n/**\n * Get adaptive HLS configuration based on network quality\n */ const getAdaptiveHLSConfig = function() {\n    let networkQuality = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"good\";\n    const baseConfig = config.hls;\n    const adaptiveSettings = adaptiveHLSConfigs[networkQuality];\n    return {\n        ...baseConfig,\n        ...adaptiveSettings\n    };\n};\n/**\n * Detect network quality based on connection information\n * This is a simple heuristic - in production you might want more sophisticated detection\n */ const detectNetworkQuality = ()=>{\n    // Check if we have network connection info\n    if ('connection' in navigator) {\n        const connection = navigator.connection;\n        if (connection) {\n            const effectiveType = connection.effectiveType;\n            const downlink = connection.downlink || 0;\n            // Use effective connection type and downlink speed\n            if (effectiveType === '4g' && downlink > 10) {\n                return \"excellent\";\n            } else if (effectiveType === '4g' || effectiveType === '3g' && downlink > 2) {\n                return \"good\";\n            } else if (effectiveType === '3g' || downlink > 0.5) {\n                return \"fair\";\n            } else {\n                return \"poor\";\n            }\n        }\n    }\n    // Fallback to good quality if we can't detect\n    return \"good\";\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return !!(cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});