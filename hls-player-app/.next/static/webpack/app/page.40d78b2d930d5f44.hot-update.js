"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Network quality tracking\n    const [networkQuality, setNetworkQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD);\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Detect network quality and get adaptive config\n                const detectedQuality = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.detectNetworkQuality)();\n                setNetworkQuality(detectedQuality);\n                const adaptiveConfig = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getAdaptiveHLSConfig)(detectedQuality);\n                console.log(\"Using \".concat(detectedQuality, \" network quality HLS config:\"), adaptiveConfig);\n                // Create new HLS instance with adaptive config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](adaptiveConfig);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Seek to live edge (reduce latency)\n    const seekToLive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[seekToLive]\": ()=>{\n            if (!hlsRef.current || !audioRef.current) return;\n            const hls = hlsRef.current;\n            if (hls.liveSyncPosition !== undefined) {\n                // Seek to near the live edge\n                const targetTime = hls.liveSyncPosition - 1 // 1 second from live edge\n                ;\n                audioRef.current.currentTime = Math.max(0, targetTime);\n            }\n        }\n    }[\"HLSPlayer.useCallback[seekToLive]\"], []);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 318,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            playerStats.latency > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: seekToLive,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                title: \"High latency: \".concat(playerStats.latency.toFixed(1), \"s - Click to seek to live\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"tMgdP6gnjqeWJfwhGnvnX/hjXR0=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});