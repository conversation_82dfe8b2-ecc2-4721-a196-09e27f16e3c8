"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Create new HLS instance with optimized config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.hls);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Seek to live edge (reduce latency)\n    const seekToLive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[seekToLive]\": ()=>{\n            if (!hlsRef.current || !audioRef.current) return;\n            const hls = hlsRef.current;\n            if (hls.liveSyncPosition !== undefined) {\n                // Seek to near the live edge\n                const targetTime = hls.liveSyncPosition - 1 // 1 second from live edge\n                ;\n                audioRef.current.currentTime = Math.max(0, targetTime);\n            }\n        }\n    }[\"HLSPlayer.useCallback[seekToLive]\"], []);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 307,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"h9ALyt0pQgXhRTmm4mSpnlokhlU=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});