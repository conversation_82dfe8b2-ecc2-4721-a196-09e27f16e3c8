"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Create new HLS instance with optimized config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.hls);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 295,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 310,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"qmP3Vy7oVwS6y4i7ZNSISZk6+bo=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});