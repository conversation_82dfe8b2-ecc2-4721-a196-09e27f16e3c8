"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Network quality tracking\n    const [networkQuality, setNetworkQuality] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD);\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Detect network quality and get adaptive config\n                const detectedQuality = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.detectNetworkQuality)();\n                setNetworkQuality(detectedQuality);\n                const adaptiveConfig = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getAdaptiveHLSConfig)(detectedQuality);\n                console.log(\"Using \".concat(detectedQuality, \" network quality HLS config:\"), adaptiveConfig);\n                // Create new HLS instance with adaptive config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](adaptiveConfig);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Seek to live edge (reduce latency)\n    const seekToLive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[seekToLive]\": ()=>{\n            if (!hlsRef.current || !audioRef.current) return;\n            const hls = hlsRef.current;\n            if (hls.liveSyncPosition !== undefined) {\n                // Seek to near the live edge\n                const targetTime = hls.liveSyncPosition - 1 // 1 second from live edge\n                ;\n                audioRef.current.currentTime = Math.max(0, targetTime);\n            }\n        }\n    }[\"HLSPlayer.useCallback[seekToLive]\"], []);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 318,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            playerStats.latency > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: seekToLive,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                title: \"High latency: \".concat(playerStats.latency.toFixed(1), \"s - Click to seek to live\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Zap, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Network\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.EXCELLENT ? \"bg-green-100 text-green-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.GOOD ? \"bg-blue-100 text-blue-800\" : networkQuality === _lib_config__WEBPACK_IMPORTED_MODULE_8__.NetworkQuality.FAIR ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\",\n                                        children: networkQuality.charAt(0).toUpperCase() + networkQuality.slice(1)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"tMgdP6gnjqeWJfwhGnvnX/hjXR0=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});