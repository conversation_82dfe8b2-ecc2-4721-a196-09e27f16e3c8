"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getHLSPlaylistUrl: () => (/* binding */ getHLSPlaylistUrl),\n/* harmony export */   getHLSSegmentUrl: () => (/* binding */ getHLSSegmentUrl),\n/* harmony export */   getHLSStatusUrl: () => (/* binding */ getHLSStatusUrl),\n/* harmony export */   isValidConfig: () => (/* binding */ isValidConfig),\n/* harmony export */   validateApiUrl: () => (/* binding */ validateApiUrl),\n/* harmony export */   validateStationId: () => (/* binding */ validateStationId)\n/* harmony export */ });\n/**\n * Configuration for the HLS Player Application\n */ // Default configuration\nconst defaultConfig = {\n    api: {\n        baseUrl: \"https://awosnew.skytraces.com\" || 0,\n        timeout: 10000\n    },\n    player: {\n        defaultStationId: \"4FL5\" || 0,\n        autoPlay: false,\n        autoRefresh: true,\n        refreshIntervals: {\n            status: 5000,\n            stats: 2000 // 2 seconds\n        }\n    },\n    hls: {\n        debug: \"development\" === 'development',\n        enableWorker: true,\n        lowLatencyMode: true,\n        // Aggressive low-latency settings\n        backBufferLength: 10,\n        maxBufferLength: 4,\n        maxMaxBufferLength: 8,\n        liveSyncDurationCount: 1,\n        liveMaxLatencyDurationCount: 2,\n        liveDurationInfinity: true,\n        // Faster loading timeouts for responsiveness\n        manifestLoadingTimeOut: 5000,\n        manifestLoadingMaxRetry: 2,\n        manifestLoadingRetryDelay: 500,\n        levelLoadingTimeOut: 5000,\n        fragLoadingTimeOut: 8000,\n        // Additional low-latency settings\n        startLevel: -1,\n        capLevelToPlayerSize: false,\n        testBandwidth: false,\n        startFragPrefetch: true,\n        progressive: true // Enable progressive loading\n    },\n    ui: {\n        theme: 'system',\n        showAdvancedStats: false\n    }\n};\n// Environment-specific overrides\nconst getEnvironmentConfig = ()=>{\n    const env = \"development\";\n    switch(env){\n        case 'development':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: true\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: true\n                }\n            };\n        case 'production':\n            return {\n                hls: {\n                    ...defaultConfig.hls,\n                    debug: false\n                },\n                ui: {\n                    ...defaultConfig.ui,\n                    showAdvancedStats: false\n                }\n            };\n        default:\n            return {};\n    }\n};\n// Merge configurations\nconst config = {\n    ...defaultConfig,\n    ...getEnvironmentConfig()\n};\n// Helper functions\nconst getApiUrl = (endpoint)=>{\n    const baseUrl = config.api.baseUrl.replace(/\\/$/, '') // Remove trailing slash\n    ;\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : \"/\".concat(endpoint);\n    return \"\".concat(baseUrl).concat(cleanEndpoint);\n};\nconst getHLSPlaylistUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/playlist.m3u8\"));\n};\nconst getHLSStatusUrl = (stationId)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/status\"));\n};\nconst getHLSSegmentUrl = (stationId, segmentName)=>{\n    return getApiUrl(\"/hls/\".concat(stationId, \"/\").concat(segmentName));\n};\n// Validation functions\nconst validateStationId = (stationId)=>{\n    return /^[A-Z0-9_]+$/.test(stationId);\n};\nconst validateApiUrl = (url)=>{\n    try {\n        new URL(url);\n        return true;\n    } catch (e) {\n        return false;\n    }\n};\n// Type guards\nconst isValidConfig = (config)=>{\n    if (!config || typeof config !== 'object') return false;\n    const cfg = config;\n    return cfg.api && typeof cfg.api === 'object' && cfg.api !== null && typeof cfg.api.baseUrl === 'string' && typeof cfg.api.timeout === 'number' && cfg.player && typeof cfg.player === 'object' && cfg.player !== null && typeof cfg.player.defaultStationId === 'string' && typeof cfg.player.autoPlay === 'boolean' && cfg.hls && typeof cfg.hls === 'object' && cfg.hls !== null && typeof cfg.hls.debug === 'boolean';\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/config.ts\n"));

/***/ })

});