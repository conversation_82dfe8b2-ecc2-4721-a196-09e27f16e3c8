"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n];\nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zap\", __iconNode);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/hls-player.tsx":
/*!***************************************!*\
  !*** ./src/components/hls-player.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HLSPlayer: () => (/* binding */ HLSPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js */ \"(app-pages-browser)/./node_modules/hls.js/dist/hls.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Pause,Play,Radio,RefreshCw,Volume2,VolumeX,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ HLSPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst HLSPlayer = (param)=>{\n    let { stationId = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.defaultStationId, apiBaseUrl = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.api.baseUrl, autoPlay = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoPlay, className = '' } = param;\n    _s();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Player state\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stream status and stats\n    const [streamStatus, setStreamStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [playerStats, setPlayerStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        bufferLength: 0,\n        currentTime: 0,\n        duration: 0,\n        isLive: false,\n        latency: 0\n    });\n    // Auto-refresh intervals\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.autoRefresh);\n    const statusIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statsIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const playlistUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSPlaylistUrl)(stationId);\n    const statusUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_8__.getHLSStatusUrl)(stationId);\n    // Fetch stream status\n    const fetchStreamStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[fetchStreamStatus]\": async ()=>{\n            try {\n                const response = await fetch(statusUrl);\n                if (response.ok) {\n                    const status = await response.json();\n                    setStreamStatus(status);\n                }\n            } catch (err) {\n                console.error('Failed to fetch stream status:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[fetchStreamStatus]\"], [\n        statusUrl\n    ]);\n    // Update player stats\n    const updatePlayerStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[updatePlayerStats]\": ()=>{\n            if (!audioRef.current || !hlsRef.current) return;\n            const audio = audioRef.current;\n            const hls = hlsRef.current;\n            const buffered = audio.buffered;\n            let bufferLength = 0;\n            if (buffered.length > 0) {\n                const bufferEnd = buffered.end(buffered.length - 1);\n                const currentTime = audio.currentTime;\n                bufferLength = bufferEnd - currentTime;\n            }\n            // Calculate latency (distance from live edge)\n            let latency = 0;\n            if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {\n                latency = hls.liveSyncPosition - audio.currentTime;\n            }\n            setPlayerStats({\n                bufferLength,\n                currentTime: audio.currentTime,\n                duration: audio.duration || 0,\n                isLive: hls.liveSyncPosition !== undefined,\n                latency: Math.max(0, latency) // Ensure non-negative\n            });\n        }\n    }[\"HLSPlayer.useCallback[updatePlayerStats]\"], []);\n    // Initialize HLS player\n    const initializePlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n            if (!audioRef.current) return;\n            const audio = audioRef.current;\n            setError(null);\n            setIsLoading(true);\n            // Check HLS support\n            if (hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n                // Destroy existing HLS instance\n                if (hlsRef.current) {\n                    hlsRef.current.destroy();\n                }\n                // Create new HLS instance with optimized config\n                const hls = new hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.hls);\n                hlsRef.current = hls;\n                hls.loadSource(playlistUrl);\n                hls.attachMedia(audio);\n                // HLS event handlers\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        setIsLoading(false);\n                        setError(null);\n                        if (autoPlay) {\n                            handlePlay();\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.ERROR, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": (event, data)=>{\n                        console.error('HLS Error:', data.type, data.details);\n                        if (data.fatal) {\n                            setError(\"Fatal error: \".concat(data.details));\n                            setIsLoading(false);\n                            switch(data.type){\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.NETWORK_ERROR:\n                                    console.log('Network error - retrying...');\n                                    hls.startLoad();\n                                    break;\n                                case hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ErrorTypes.MEDIA_ERROR:\n                                    console.log('Media error - recovering...');\n                                    hls.recoverMediaError();\n                                    break;\n                                default:\n                                    console.log('Unrecoverable error');\n                                    break;\n                            }\n                        }\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n                hls.on(hls_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.FRAG_LOADED, {\n                    \"HLSPlayer.useCallback[initializePlayer]\": ()=>{\n                        updatePlayerStats();\n                    }\n                }[\"HLSPlayer.useCallback[initializePlayer]\"]);\n            } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {\n                // Native HLS support (Safari)\n                audio.src = playlistUrl;\n                setIsLoading(false);\n                if (autoPlay) {\n                    handlePlay();\n                }\n            } else {\n                setError('HLS not supported in this browser');\n                setIsLoading(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[initializePlayer]\"], [\n        playlistUrl,\n        autoPlay,\n        updatePlayerStats\n    ]);\n    // Play handler\n    const handlePlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePlay]\": async ()=>{\n            if (!audioRef.current) return;\n            try {\n                if (hlsRef.current) {\n                    hlsRef.current.startLoad();\n                }\n                await audioRef.current.play();\n                setIsPlaying(true);\n                setError(null);\n            } catch (err) {\n                setError('Playback failed - check audio permissions');\n                console.error('Playback error:', err);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handlePlay]\"], []);\n    // Pause handler\n    const handlePause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handlePause]\": ()=>{\n            if (!audioRef.current) return;\n            audioRef.current.pause();\n            setIsPlaying(false);\n        }\n    }[\"HLSPlayer.useCallback[handlePause]\"], []);\n    // Toggle play/pause\n    const togglePlayPause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[togglePlayPause]\": ()=>{\n            if (isPlaying) {\n                handlePause();\n            } else {\n                handlePlay();\n            }\n        }\n    }[\"HLSPlayer.useCallback[togglePlayPause]\"], [\n        isPlaying,\n        handlePlay,\n        handlePause\n    ]);\n    // Toggle mute\n    const toggleMute = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[toggleMute]\": ()=>{\n            if (!audioRef.current) return;\n            const newMuted = !isMuted;\n            audioRef.current.muted = newMuted;\n            setIsMuted(newMuted);\n        }\n    }[\"HLSPlayer.useCallback[toggleMute]\"], [\n        isMuted\n    ]);\n    // Handle volume change\n    const handleVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[handleVolumeChange]\": (newVolume)=>{\n            if (!audioRef.current) return;\n            audioRef.current.volume = newVolume;\n            setVolume(newVolume);\n            if (newVolume === 0) {\n                setIsMuted(true);\n            } else if (isMuted) {\n                setIsMuted(false);\n            }\n        }\n    }[\"HLSPlayer.useCallback[handleVolumeChange]\"], [\n        isMuted\n    ]);\n    // Refresh player\n    const refreshPlayer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[refreshPlayer]\": ()=>{\n            setIsLoading(true);\n            initializePlayer();\n            fetchStreamStatus();\n        }\n    }[\"HLSPlayer.useCallback[refreshPlayer]\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Seek to live edge (reduce latency)\n    const seekToLive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HLSPlayer.useCallback[seekToLive]\": ()=>{\n            if (!hlsRef.current || !audioRef.current) return;\n            const hls = hlsRef.current;\n            if (hls.liveSyncPosition !== undefined) {\n                // Seek to near the live edge\n                const targetTime = hls.liveSyncPosition - 1 // 1 second from live edge\n                ;\n                audioRef.current.currentTime = Math.max(0, targetTime);\n            }\n        }\n    }[\"HLSPlayer.useCallback[seekToLive]\"], []);\n    // Setup intervals for auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            if (autoRefresh) {\n                // Fetch status every configured interval\n                statusIntervalRef.current = setInterval(fetchStreamStatus, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.status);\n                // Update stats every configured interval\n                statsIntervalRef.current = setInterval(updatePlayerStats, _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.player.refreshIntervals.stats);\n            }\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (statusIntervalRef.current) {\n                        clearInterval(statusIntervalRef.current);\n                    }\n                    if (statsIntervalRef.current) {\n                        clearInterval(statsIntervalRef.current);\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        autoRefresh,\n        fetchStreamStatus,\n        updatePlayerStats\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            initializePlayer();\n            fetchStreamStatus();\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    if (hlsRef.current) {\n                        hlsRef.current.destroy();\n                    }\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], [\n        initializePlayer,\n        fetchStreamStatus\n    ]);\n    // Audio event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HLSPlayer.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const handleLoadStart = {\n                \"HLSPlayer.useEffect.handleLoadStart\": ()=>setIsLoading(true)\n            }[\"HLSPlayer.useEffect.handleLoadStart\"];\n            const handleCanPlay = {\n                \"HLSPlayer.useEffect.handleCanPlay\": ()=>setIsLoading(false)\n            }[\"HLSPlayer.useEffect.handleCanPlay\"];\n            const handlePlay = {\n                \"HLSPlayer.useEffect.handlePlay\": ()=>setIsPlaying(true)\n            }[\"HLSPlayer.useEffect.handlePlay\"];\n            const handlePause = {\n                \"HLSPlayer.useEffect.handlePause\": ()=>setIsPlaying(false)\n            }[\"HLSPlayer.useEffect.handlePause\"];\n            const handleVolumeChange = {\n                \"HLSPlayer.useEffect.handleVolumeChange\": ()=>{\n                    setVolume(audio.volume);\n                    setIsMuted(audio.muted);\n                }\n            }[\"HLSPlayer.useEffect.handleVolumeChange\"];\n            audio.addEventListener('loadstart', handleLoadStart);\n            audio.addEventListener('canplay', handleCanPlay);\n            audio.addEventListener('play', handlePlay);\n            audio.addEventListener('pause', handlePause);\n            audio.addEventListener('volumechange', handleVolumeChange);\n            return ({\n                \"HLSPlayer.useEffect\": ()=>{\n                    audio.removeEventListener('loadstart', handleLoadStart);\n                    audio.removeEventListener('canplay', handleCanPlay);\n                    audio.removeEventListener('play', handlePlay);\n                    audio.removeEventListener('pause', handlePause);\n                    audio.removeEventListener('volumechange', handleVolumeChange);\n                }\n            })[\"HLSPlayer.useEffect\"];\n        }\n    }[\"HLSPlayer.useEffect\"], []);\n    const getStatusBadge = ()=>{\n        if (!streamStatus) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: \"secondary\",\n            children: \"Unknown\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n            lineNumber: 308,\n            columnNumber: 31\n        }, undefined);\n        switch(streamStatus.status){\n            case 'active':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 66\n                        }, undefined),\n                        \"Active\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 'inactive':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 43\n                        }, undefined),\n                        \"Inactive\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 16\n                }, undefined);\n            case 'not_found':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 45\n                        }, undefined),\n                        \"Not Found\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            \"AWOS HLS Stream Player\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        children: [\n                            \"Live audio streaming from Ridge Landing Airpark (VPS) - Station \",\n                            stationId\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-6\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        className: \"hidden\",\n                        preload: \"none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: togglePlayPause,\n                                disabled: isLoading,\n                                size: \"lg\",\n                                className: \"w-16 h-16 rounded-full\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined) : isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: toggleMute,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 26\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: volume,\n                                        onChange: (e)=>handleVolumeChange(parseFloat(e.target.value)),\n                                        className: \"w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: refreshPlayer,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            playerStats.latency > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: seekToLive,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                title: \"High latency: \".concat(playerStats.latency.toFixed(1), \"s - Click to seek to live\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    getStatusBadge()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Segments\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.segment_count) || 0\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Buffer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Latency\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        className: playerStats.latency > 5 ? \"bg-yellow-100\" : \"bg-green-100\",\n                                        children: [\n                                            playerStats.latency.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Pause_Play_Radio_RefreshCw_Volume2_VolumeX_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            playerStats.isLive ? 'Live' : 'VOD'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, undefined),\n                    playerStats.bufferLength > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Buffer Health\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            playerStats.bufferLength.toFixed(1),\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                value: Math.min(playerStats.bufferLength / 30 * 100, 100),\n                                className: \"h-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Auto-refresh status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setAutoRefresh(!autoRefresh),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: autoRefresh ? 'ON' : 'OFF'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined),\n                    (streamStatus === null || streamStatus === void 0 ? void 0 : streamStatus.last_update) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-muted-foreground text-center\",\n                        children: [\n                            \"Last updated: \",\n                            new Date(streamStatus.last_update * 1000).toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/SayWeather_ridge/hls-player-app/src/components/hls-player.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HLSPlayer, \"h9ALyt0pQgXhRTmm4mSpnlokhlU=\");\n_c = HLSPlayer;\nvar _c;\n$RefreshReg$(_c, \"HLSPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hls-player.tsx\n"));

/***/ })

});