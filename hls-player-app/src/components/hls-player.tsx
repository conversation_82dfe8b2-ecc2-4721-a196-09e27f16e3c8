'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import Hls from 'hls.js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  RefreshCw,
  Activity,
  AlertCircle,
  CheckCircle,
  Radio,

} from 'lucide-react'
import { config, getHLSPlaylistUrl, getHLSStatusUrl, getAdaptiveHLSConfig, detectNetworkQualityFromAPI, NetworkQuality } from '@/lib/config'
import type { HLSPlayerProps, StreamStatus, PlayerStats } from '@/types'

export const HLSPlayer: React.FC<HLSPlayerProps> = ({
  stationId = config.player.defaultStationId,
  apiBaseUrl = config.api.baseUrl,
  autoPlay = config.player.autoPlay,
  className = ''
}) => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  
  // Player state
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Stream status and stats
  const [streamStatus, setStreamStatus] = useState<StreamStatus | null>(null)
  const [playerStats, setPlayerStats] = useState<PlayerStats>({
    bufferLength: 0,
    currentTime: 0,
    duration: 0,
    isLive: false,
    latency: 0
  })

  // Network quality tracking
  const [networkQuality, setNetworkQuality] = useState<NetworkQuality>(NetworkQuality.GOOD)
  
  // Auto-refresh intervals
  const [autoRefresh, setAutoRefresh] = useState(config.player.autoRefresh)
  const statusIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const statsIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const playlistUrl = getHLSPlaylistUrl(stationId)
  const statusUrl = getHLSStatusUrl(stationId)

  // Fetch stream status
  const fetchStreamStatus = useCallback(async () => {
    try {
      const response = await fetch(statusUrl)
      if (response.ok) {
        const status: StreamStatus = await response.json()
        setStreamStatus(status)
      }
    } catch (err) {
      console.error('Failed to fetch stream status:', err)
    }
  }, [statusUrl])

  // Update player stats
  const updatePlayerStats = useCallback(() => {
    if (!audioRef.current || !hlsRef.current) return

    const audio = audioRef.current
    const hls = hlsRef.current

    const buffered = audio.buffered
    let bufferLength = 0

    if (buffered.length > 0) {
      const bufferEnd = buffered.end(buffered.length - 1)
      const currentTime = audio.currentTime
      bufferLength = bufferEnd - currentTime
    }

    // Calculate latency (distance from live edge)
    let latency = 0
    if (hls.liveSyncPosition !== undefined && audio.currentTime > 0) {
      latency = hls.liveSyncPosition - audio.currentTime
    }

    setPlayerStats({
      bufferLength,
      currentTime: audio.currentTime,
      duration: audio.duration || 0,
      isLive: hls.liveSyncPosition !== undefined,
      latency: Math.max(0, latency) // Ensure non-negative
    })
  }, [])

  // Initialize HLS player
  const initializePlayer = useCallback(() => {
    if (!audioRef.current) return

    const audio = audioRef.current
    setError(null)
    setIsLoading(true)

    // Check HLS support
    if (Hls.isSupported()) {
      // Destroy existing HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }

      // Detect network quality based on API response time
      const detectedQuality = await detectNetworkQualityFromAPI(statusUrl)
      setNetworkQuality(detectedQuality)
      const adaptiveConfig = getAdaptiveHLSConfig(detectedQuality)

      console.log(`Using ${detectedQuality} network quality HLS config (API-based):`, adaptiveConfig)

      // Create new HLS instance with adaptive config
      const hls = new Hls(adaptiveConfig)

      hlsRef.current = hls
      hls.loadSource(playlistUrl)
      hls.attachMedia(audio)

      // HLS event handlers
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        setIsLoading(false)
        setError(null)
        if (autoPlay) {
          handlePlay()
        }
      })

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS Error:', data.type, data.details)
        
        if (data.fatal) {
          setError(`Fatal error: ${data.details}`)
          setIsLoading(false)
          
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error - retrying...')
              hls.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error - recovering...')
              hls.recoverMediaError()
              break
            default:
              console.log('Unrecoverable error')
              break
          }
        }
      })

      hls.on(Hls.Events.FRAG_LOADED, () => {
        updatePlayerStats()
      })

    } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      audio.src = playlistUrl
      setIsLoading(false)
      if (autoPlay) {
        handlePlay()
      }
    } else {
      setError('HLS not supported in this browser')
      setIsLoading(false)
    }
  }, [playlistUrl, statusUrl, autoPlay, updatePlayerStats])

  // Play handler
  const handlePlay = useCallback(async () => {
    if (!audioRef.current) return

    try {
      if (hlsRef.current) {
        hlsRef.current.startLoad()
      }
      await audioRef.current.play()
      setIsPlaying(true)
      setError(null)
    } catch (err) {
      setError('Playback failed - check audio permissions')
      console.error('Playback error:', err)
    }
  }, [])

  // Pause handler
  const handlePause = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.pause()
    setIsPlaying(false)
  }, [])

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [isPlaying, handlePlay, handlePause])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return
    const newMuted = !isMuted
    audioRef.current.muted = newMuted
    setIsMuted(newMuted)
  }, [isMuted])

  // Handle volume change
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!audioRef.current) return
    audioRef.current.volume = newVolume
    setVolume(newVolume)
    if (newVolume === 0) {
      setIsMuted(true)
    } else if (isMuted) {
      setIsMuted(false)
    }
  }, [isMuted])

  // Refresh player
  const refreshPlayer = useCallback(() => {
    setIsLoading(true)
    initializePlayer()
    fetchStreamStatus()
  }, [initializePlayer, fetchStreamStatus])



  // Setup intervals for auto-refresh
  useEffect(() => {
    if (autoRefresh) {
      // Fetch status every configured interval
      statusIntervalRef.current = setInterval(fetchStreamStatus, config.player.refreshIntervals.status)

      // Update stats every configured interval
      statsIntervalRef.current = setInterval(updatePlayerStats, config.player.refreshIntervals.stats)
    }

    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current)
      }
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current)
      }
    }
  }, [autoRefresh, fetchStreamStatus, updatePlayerStats])

  // Initialize on mount
  useEffect(() => {
    initializePlayer()
    fetchStreamStatus()

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
    }
  }, [initializePlayer, fetchStreamStatus])

  // Audio event listeners
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleLoadStart = () => setIsLoading(true)
    const handleCanPlay = () => setIsLoading(false)
    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleVolumeChange = () => {
      setVolume(audio.volume)
      setIsMuted(audio.muted)
    }

    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('canplay', handleCanPlay)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('volumechange', handleVolumeChange)

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('canplay', handleCanPlay)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('volumechange', handleVolumeChange)
    }
  }, [])

  const getStatusBadge = () => {
    if (!streamStatus) return <Badge variant="secondary">Unknown</Badge>

    switch (streamStatus.status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>
      case 'inactive':
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />Inactive</Badge>
      case 'not_found':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Not Found</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Radio className="w-5 h-5" />
          AWOS HLS Stream Player
        </CardTitle>
        <CardDescription>
          Live audio streaming from Ridge Landing Airpark (VPS) - Station {stationId}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Audio Element */}
        <audio
          ref={audioRef}
          className="hidden"
          preload="none"
        />

        {/* Player Controls */}
        <div className="flex items-center justify-center gap-4">
          <Button
            onClick={togglePlayPause}
            disabled={isLoading}
            size="lg"
            className="w-16 h-16 rounded-full"
          >
            {isLoading ? (
              <RefreshCw className="w-6 h-6 animate-spin" />
            ) : isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6" />
            )}
          </Button>

          <div className="flex items-center gap-2">
            <Button
              onClick={toggleMute}
              variant="outline"
              size="sm"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </Button>

            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-20"
            />
          </div>

          <Button
            onClick={refreshPlayer}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="w-4 h-4" />
          </Button>


        </div>

        {/* Stream Status */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Status</span>
            {getStatusBadge()}
          </div>

          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Segments</span>
            <Badge variant="outline">{streamStatus?.segment_count || 0}</Badge>
          </div>

          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Buffer</span>
            <Badge variant="outline">{playerStats.bufferLength.toFixed(1)}s</Badge>
          </div>

          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Latency</span>
            <Badge variant="outline" className={playerStats.latency > 5 ? "bg-yellow-100" : "bg-green-100"}>
              {playerStats.latency.toFixed(1)}s
            </Badge>
          </div>

          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Type</span>
            <Badge variant="outline">
              <Activity className="w-3 h-3 mr-1" />
              {playerStats.isLive ? 'Live' : 'VOD'}
            </Badge>
          </div>

          <div className="flex flex-col items-center gap-1">
            <span className="text-muted-foreground">Network</span>
            <Badge
              variant="outline"
              className={
                networkQuality === NetworkQuality.EXCELLENT ? "bg-green-100 text-green-800" :
                networkQuality === NetworkQuality.GOOD ? "bg-blue-100 text-blue-800" :
                networkQuality === NetworkQuality.FAIR ? "bg-yellow-100 text-yellow-800" :
                "bg-red-100 text-red-800"
              }
            >
              {networkQuality.charAt(0).toUpperCase() + networkQuality.slice(1)}
            </Badge>
          </div>
        </div>

        {/* Buffer Progress */}
        {playerStats.bufferLength > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Buffer Health</span>
              <span>{playerStats.bufferLength.toFixed(1)}s</span>
            </div>
            <Progress
              value={Math.min((playerStats.bufferLength / 30) * 100, 100)}
              className="h-2"
            />
          </div>
        )}

        {/* Auto-refresh Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Auto-refresh status</span>
          <Button
            onClick={() => setAutoRefresh(!autoRefresh)}
            variant="outline"
            size="sm"
          >
            {autoRefresh ? 'ON' : 'OFF'}
          </Button>
        </div>

        {/* Last Update */}
        {streamStatus?.last_update && (
          <div className="text-xs text-muted-foreground text-center">
            Last updated: {new Date(streamStatus.last_update * 1000).toLocaleTimeString()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
