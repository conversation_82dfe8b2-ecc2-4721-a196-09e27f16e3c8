/**
 * Type definitions for the HLS Player Application
 */

// Stream Status Types
export interface StreamStatus {
  station_id: string
  status: 'active' | 'inactive' | 'not_found'
  playlist_exists: boolean
  segment_count: number
  last_update?: number
}

// Player Statistics
export interface PlayerStats {
  bufferLength: number
  currentTime: number
  duration: number
  isLive: boolean
  latency: number
}

// Player State
export interface PlayerState {
  isPlaying: boolean
  isMuted: boolean
  volume: number
  isLoading: boolean
  error: string | null
}

// HLS Player Props
export interface HLSPlayerProps {
  stationId?: string
  apiBaseUrl?: string
  autoPlay?: boolean
  className?: string
  onStatusChange?: (status: StreamStatus) => void
  onStatsChange?: (stats: PlayerStats) => void
  onStateChange?: (state: PlayerState) => void
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  timestamp: number
}

export interface ErrorResponse {
  error: string
  details?: string
  code?: number
}

// HLS Configuration
export interface HLSConfig {
  debug?: boolean
  enableWorker?: boolean
  lowLatencyMode?: boolean
  backBufferLength?: number
  maxBufferLength?: number
  maxMaxBufferLength?: number
  liveSyncDurationCount?: number
  liveMaxLatencyDurationCount?: number
  liveDurationInfinity?: boolean
  manifestLoadingTimeOut?: number
  manifestLoadingMaxRetry?: number
  manifestLoadingRetryDelay?: number
  levelLoadingTimeOut?: number
  fragLoadingTimeOut?: number
}

// Event Types
export type PlayerEvent = 
  | 'play'
  | 'pause'
  | 'loading'
  | 'error'
  | 'statusUpdate'
  | 'statsUpdate'

export interface PlayerEventData {
  type: PlayerEvent
  timestamp: number
  data?: unknown
}

// Utility Types
export type StationId = string
export type ApiUrl = string
export type Timestamp = number

// Component Props
export interface StatusBadgeProps {
  status: StreamStatus['status']
  className?: string
}

export interface ProgressBarProps {
  value: number
  max?: number
  className?: string
  showLabel?: boolean
}

export interface VolumeControlProps {
  volume: number
  isMuted: boolean
  onVolumeChange: (volume: number) => void
  onMuteToggle: () => void
  className?: string
}

// Hook Return Types
export interface UseHLSPlayerReturn {
  // State
  isPlaying: boolean
  isMuted: boolean
  volume: number
  isLoading: boolean
  error: string | null
  streamStatus: StreamStatus | null
  playerStats: PlayerStats
  
  // Actions
  play: () => Promise<void>
  pause: () => void
  togglePlayPause: () => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  refresh: () => void
  
  // Refs
  audioRef: React.RefObject<HTMLAudioElement | null>
}

export interface UseStreamStatusReturn {
  status: StreamStatus | null
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
}

// Environment Variables
export interface EnvironmentVariables {
  NEXT_PUBLIC_API_BASE_URL?: string
  NEXT_PUBLIC_STATION_ID?: string
  NODE_ENV: 'development' | 'production' | 'test'
}

// Export all types
export * from './index'
