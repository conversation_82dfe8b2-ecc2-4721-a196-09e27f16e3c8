import { useEffect, useRef, useState, useCallback } from 'react'
import Hls from 'hls.js'
import { config, getHLSPlaylistUrl, getHLSStatusUrl } from '@/lib/config'
import type { StreamStatus, PlayerStats, UseHLSPlayerReturn } from '@/types'

export const useHLSPlayer = (
  stationId: string = config.player.defaultStationId,
  apiBaseUrl: string = config.api.baseUrl
): UseHLSPlayerReturn => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  
  // Player state
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Stream status and stats
  const [streamStatus, setStreamStatus] = useState<StreamStatus | null>(null)
  const [playerStats, setPlayerStats] = useState<PlayerStats>({
    bufferLength: 0,
    currentTime: 0,
    duration: 0,
    isLive: false
  })
  
  // Auto-refresh intervals
  const [autoRefresh, setAutoRefresh] = useState(config.player.autoRefresh)
  const statusIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const statsIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const playlistUrl = getHLSPlaylistUrl(stationId)
  const statusUrl = getHLSStatusUrl(stationId)

  // Fetch stream status
  const fetchStreamStatus = useCallback(async () => {
    try {
      const response = await fetch(statusUrl, {
        signal: AbortSignal.timeout(config.api.timeout)
      })
      if (response.ok) {
        const status: StreamStatus = await response.json()
        setStreamStatus(status)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (err) {
      console.error('Failed to fetch stream status:', err)
      if (err instanceof Error) {
        setError(`Status fetch failed: ${err.message}`)
      }
    }
  }, [statusUrl])

  // Update player stats
  const updatePlayerStats = useCallback(() => {
    if (!audioRef.current || !hlsRef.current) return

    const audio = audioRef.current
    const hls = hlsRef.current

    const buffered = audio.buffered
    let bufferLength = 0
    
    if (buffered.length > 0) {
      const bufferEnd = buffered.end(buffered.length - 1)
      const currentTime = audio.currentTime
      bufferLength = bufferEnd - currentTime
    }

    setPlayerStats({
      bufferLength,
      currentTime: audio.currentTime,
      duration: audio.duration || 0,
      isLive: hls.liveSyncPosition !== undefined
    })
  }, [])

  // Initialize HLS player
  const initializePlayer = useCallback(() => {
    if (!audioRef.current) return

    const audio = audioRef.current
    setError(null)
    setIsLoading(true)

    // Check HLS support
    if (Hls.isSupported()) {
      // Destroy existing HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }

      // Create new HLS instance with config
      const hls = new Hls(config.hls)
      hlsRef.current = hls
      hls.loadSource(playlistUrl)
      hls.attachMedia(audio)

      // HLS event handlers
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        setIsLoading(false)
        setError(null)
      })

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS Error:', data.type, data.details, data)
        
        if (data.fatal) {
          setIsLoading(false)
          
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              setError('Network error - check your connection')
              setTimeout(() => {
                console.log('Retrying after network error...')
                hls.startLoad()
              }, 3000)
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              setError('Media error - attempting recovery')
              hls.recoverMediaError()
              break
            default:
              setError(`Fatal error: ${data.details}`)
              break
          }
        } else {
          // Non-fatal errors
          console.warn('Non-fatal HLS error:', data.details)
        }
      })

      hls.on(Hls.Events.FRAG_LOADED, () => {
        updatePlayerStats()
      })

      hls.on(Hls.Events.LEVEL_LOADED, () => {
        setError(null) // Clear errors on successful load
      })

    } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      audio.src = playlistUrl
      setIsLoading(false)
    } else {
      setError('HLS not supported in this browser')
      setIsLoading(false)
    }
  }, [playlistUrl, updatePlayerStats])

  // Play handler
  const play = useCallback(async () => {
    if (!audioRef.current) return

    try {
      if (hlsRef.current) {
        hlsRef.current.startLoad()
      }
      await audioRef.current.play()
      setIsPlaying(true)
      setError(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown playback error'
      setError(`Playback failed: ${errorMessage}`)
      console.error('Playback error:', err)
    }
  }, [])

  // Pause handler
  const pause = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.pause()
    setIsPlaying(false)
  }, [])

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPlaying) {
      pause()
    } else {
      play()
    }
  }, [isPlaying, play, pause])

  // Set volume
  const setVolumeHandler = useCallback((newVolume: number) => {
    if (!audioRef.current) return
    const clampedVolume = Math.max(0, Math.min(1, newVolume))
    audioRef.current.volume = clampedVolume
    setVolume(clampedVolume)
    if (clampedVolume === 0) {
      setIsMuted(true)
    } else if (isMuted) {
      setIsMuted(false)
    }
  }, [isMuted])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return
    const newMuted = !isMuted
    audioRef.current.muted = newMuted
    setIsMuted(newMuted)
  }, [isMuted])

  // Refresh player
  const refresh = useCallback(() => {
    setIsLoading(true)
    setError(null)
    initializePlayer()
    fetchStreamStatus()
  }, [initializePlayer, fetchStreamStatus])

  // Setup intervals for auto-refresh
  useEffect(() => {
    if (autoRefresh) {
      statusIntervalRef.current = setInterval(fetchStreamStatus, config.player.refreshIntervals.status)
      statsIntervalRef.current = setInterval(updatePlayerStats, config.player.refreshIntervals.stats)
    }

    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current)
      }
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current)
      }
    }
  }, [autoRefresh, fetchStreamStatus, updatePlayerStats])

  // Initialize on mount
  useEffect(() => {
    initializePlayer()
    fetchStreamStatus()

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
    }
  }, [initializePlayer, fetchStreamStatus])

  // Audio event listeners
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleLoadStart = () => setIsLoading(true)
    const handleCanPlay = () => setIsLoading(false)
    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleVolumeChange = () => {
      setVolume(audio.volume)
      setIsMuted(audio.muted)
    }
    const handleError = () => {
      setError('Audio playback error')
      setIsLoading(false)
    }

    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('canplay', handleCanPlay)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('volumechange', handleVolumeChange)
    audio.addEventListener('error', handleError)

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('canplay', handleCanPlay)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('volumechange', handleVolumeChange)
      audio.removeEventListener('error', handleError)
    }
  }, [])

  return {
    // State
    isPlaying,
    isMuted,
    volume,
    isLoading,
    error,
    streamStatus,
    playerStats,
    
    // Actions
    play,
    pause,
    togglePlayPause,
    setVolume: setVolumeHandler,
    toggleMute,
    refresh,
    
    // Refs
    audioRef
  }
}
