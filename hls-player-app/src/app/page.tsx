import { HLSPlayer } from '@/components/hls-player'
import { config } from '@/lib/config'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            Ridge Landing Airpark
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400">
            Live AWOS Audio Stream
          </p>
          <p className="text-sm text-slate-500 dark:text-slate-500 mt-1">
            Real-time weather information from VPS
          </p>
        </div>

        {/* HLS Player */}
        <div className="flex justify-center">
          <HLSPlayer
            stationId={config.player.defaultStationId}
            apiBaseUrl={config.api.baseUrl}
            autoPlay={config.player.autoPlay}
          />
        </div>

        {/* Footer */}
        <footer className="mt-16 text-center text-sm text-slate-500 dark:text-slate-400">
          <p>
            Built with Next.js, shadcn/ui, and HLS.js
          </p>
          <p className="mt-1">
            © 2025 Ridge Landing Airpark Weather System
          </p>
        </footer>
      </div>
    </div>
  );
}
