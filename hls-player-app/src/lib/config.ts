/**
 * Configuration for the HLS Player Application
 */

export interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
  }
  player: {
    defaultStationId: string
    autoPlay: boolean
    autoRefresh: boolean
    refreshIntervals: {
      status: number
      stats: number
    }
  }
  hls: {
    debug: boolean
    enableWorker: boolean
    lowLatencyMode: boolean
    backBufferLength: number
    maxBufferLength: number
    maxMaxBufferLength: number
    liveSyncDurationCount: number
    liveMaxLatencyDurationCount: number
    liveDurationInfinity: boolean
    manifestLoadingTimeOut: number
    manifestLoadingMaxRetry: number
    manifestLoadingRetryDelay: number
    levelLoadingTimeOut: number
    fragLoadingTimeOut: number
  }
  ui: {
    theme: 'light' | 'dark' | 'system'
    showAdvancedStats: boolean
  }
}

// Default configuration
const defaultConfig: AppConfig = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://awosnew.skytraces.com',
    timeout: 10000
  },
  player: {
    defaultStationId: process.env.NEXT_PUBLIC_STATION_ID || '4FL5',
    autoPlay: false,
    autoRefresh: true,
    refreshIntervals: {
      status: 5000, // 5 seconds
      stats: 2000   // 2 seconds
    }
  },
  hls: {
    debug: process.env.NODE_ENV === 'development',
    enableWorker: true,
    lowLatencyMode: true,
    // Aggressive low-latency settings
    backBufferLength: 10,        // Reduced from 30
    maxBufferLength: 4,          // Reduced from 30 - keep only ~4 seconds
    maxMaxBufferLength: 8,       // Reduced from 60 - absolute max 8 seconds
    liveSyncDurationCount: 1,    // Reduced from 3 - stay closer to live edge
    liveMaxLatencyDurationCount: 2, // Reduced from 5 - max 2 segments behind
    liveDurationInfinity: true,
    // Faster loading timeouts for responsiveness
    manifestLoadingTimeOut: 5000,   // Reduced from 10000
    manifestLoadingMaxRetry: 2,     // Reduced from 3
    manifestLoadingRetryDelay: 500, // Reduced from 1000
    levelLoadingTimeOut: 5000,      // Reduced from 10000
    fragLoadingTimeOut: 8000        // Reduced from 20000
  },
  ui: {
    theme: 'system',
    showAdvancedStats: false
  }
}

// Environment-specific overrides
const getEnvironmentConfig = (): Partial<AppConfig> => {
  const env = process.env.NODE_ENV

  switch (env) {
    case 'development':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: true
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: true
        }
      }
    
    case 'production':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: false
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: false
        }
      }
    
    default:
      return {}
  }
}

// Merge configurations
export const config: AppConfig = {
  ...defaultConfig,
  ...getEnvironmentConfig()
}

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '') // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${cleanEndpoint}`
}

export const getHLSPlaylistUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/playlist.m3u8`)
}

export const getHLSStatusUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/status`)
}

export const getHLSSegmentUrl = (stationId: string, segmentName: string): string => {
  return getApiUrl(`/hls/${stationId}/${segmentName}`)
}

// Validation functions
export const validateStationId = (stationId: string): boolean => {
  return /^[A-Z0-9_]+$/.test(stationId)
}

export const validateApiUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Type guards
export const isValidConfig = (config: unknown): config is AppConfig => {
  if (!config || typeof config !== 'object') return false

  const cfg = config as Record<string, unknown>

  return !!(
    cfg.api &&
    typeof cfg.api === 'object' &&
    cfg.api !== null &&
    typeof (cfg.api as Record<string, unknown>).baseUrl === 'string' &&
    typeof (cfg.api as Record<string, unknown>).timeout === 'number' &&
    cfg.player &&
    typeof cfg.player === 'object' &&
    cfg.player !== null &&
    typeof (cfg.player as Record<string, unknown>).defaultStationId === 'string' &&
    typeof (cfg.player as Record<string, unknown>).autoPlay === 'boolean' &&
    cfg.hls &&
    typeof cfg.hls === 'object' &&
    cfg.hls !== null &&
    typeof (cfg.hls as Record<string, unknown>).debug === 'boolean'
  )
}

export default config
