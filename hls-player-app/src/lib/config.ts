/**
 * Configuration for the HLS Player Application
 */

export interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
  }
  player: {
    defaultStationId: string
    autoPlay: boolean
    autoRefresh: boolean
    refreshIntervals: {
      status: number
      stats: number
    }
  }
  hls: {
    debug: boolean
    enableWorker: boolean
    lowLatencyMode: boolean
    backBufferLength: number
    maxBufferLength: number
    maxMaxBufferLength: number
    liveSyncDurationCount: number
    liveMaxLatencyDurationCount: number
    liveDurationInfinity: boolean
    manifestLoadingTimeOut: number
    manifestLoadingMaxRetry: number
    manifestLoadingRetryDelay: number
    levelLoadingTimeOut: number
    fragLoadingTimeOut: number
    // Additional adaptive settings
    startLevel?: number
    capLevelToPlayerSize?: boolean
    testBandwidth?: boolean
    abrEwmaFastLive?: number
    abrEwmaSlowLive?: number
    maxStarvationDelay?: number
    maxLoadingDelay?: number
  }
  ui: {
    theme: 'light' | 'dark' | 'system'
    showAdvancedStats: boolean
  }
}

// Default configuration
const defaultConfig: AppConfig = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://awosnew.skytraces.com',
    timeout: 10000
  },
  player: {
    defaultStationId: process.env.NEXT_PUBLIC_STATION_ID || '4FL5',
    autoPlay: false,
    autoRefresh: true,
    refreshIntervals: {
      status: 5000, // 5 seconds
      stats: 2000   // 2 seconds
    }
  },
  hls: {
    debug: process.env.NODE_ENV === 'development',
    enableWorker: true,
    lowLatencyMode: true,
    // Adaptive low-latency settings that balance performance and reliability
    backBufferLength: 8,         // Moderate buffer for stability
    maxBufferLength: 6,          // 6 seconds - balance between latency and reliability
    maxMaxBufferLength: 12,      // 12 seconds max - allows recovery from network issues
    liveSyncDurationCount: 2,    // Start 2 segments behind live edge (safer than 1)
    liveMaxLatencyDurationCount: 4, // Allow up to 4 segments behind before rebuffering
    liveDurationInfinity: true,
    // Balanced loading timeouts - faster than defaults but not too aggressive
    manifestLoadingTimeOut: 8000,   // 8 seconds - balance between speed and reliability
    manifestLoadingMaxRetry: 3,     // Keep 3 retries for reliability
    manifestLoadingRetryDelay: 750, // 750ms - faster than default but not too aggressive
    levelLoadingTimeOut: 8000,      // 8 seconds for level loading
    fragLoadingTimeOut: 10000,      // 10 seconds for fragment loading
    // Additional adaptive settings
    startLevel: -1,                 // Auto-select initial quality
    capLevelToPlayerSize: false,    // Don't limit quality to player size
    testBandwidth: true,            // Test bandwidth for adaptive streaming
    abrEwmaFastLive: 3.0,          // Faster adaptation for live streams
    abrEwmaSlowLive: 9.0,          // Slower adaptation to avoid oscillation
    maxStarvationDelay: 4,         // Max 4 seconds starvation before quality drop
    maxLoadingDelay: 4             // Max 4 seconds loading delay before retry
  },
  ui: {
    theme: 'system',
    showAdvancedStats: false
  }
}

// Environment-specific overrides
const getEnvironmentConfig = (): Partial<AppConfig> => {
  const env = process.env.NODE_ENV

  switch (env) {
    case 'development':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: true
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: true
        }
      }
    
    case 'production':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: false
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: false
        }
      }
    
    default:
      return {}
  }
}

// Merge configurations
export const config: AppConfig = {
  ...defaultConfig,
  ...getEnvironmentConfig()
}

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '') // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${cleanEndpoint}`
}

export const getHLSPlaylistUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/playlist.m3u8`)
}

export const getHLSStatusUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/status`)
}

export const getHLSSegmentUrl = (stationId: string, segmentName: string): string => {
  return getApiUrl(`/hls/${stationId}/${segmentName}`)
}

/**
 * Network quality levels for adaptive HLS configuration
 */
export enum NetworkQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

/**
 * Adaptive HLS configurations based on network quality
 */
export const adaptiveHLSConfigs = {
  [NetworkQuality.EXCELLENT]: {
    // Ultra-low latency for excellent connections
    backBufferLength: 4,
    maxBufferLength: 3,
    maxMaxBufferLength: 6,
    liveSyncDurationCount: 1,
    liveMaxLatencyDurationCount: 2,
    manifestLoadingTimeOut: 4000,
    manifestLoadingMaxRetry: 2,
    manifestLoadingRetryDelay: 300,
    levelLoadingTimeOut: 4000,
    fragLoadingTimeOut: 6000,
    maxStarvationDelay: 2,
    maxLoadingDelay: 2
  },
  [NetworkQuality.GOOD]: {
    // Balanced settings (default)
    backBufferLength: 8,
    maxBufferLength: 6,
    maxMaxBufferLength: 12,
    liveSyncDurationCount: 2,
    liveMaxLatencyDurationCount: 4,
    manifestLoadingTimeOut: 8000,
    manifestLoadingMaxRetry: 3,
    manifestLoadingRetryDelay: 750,
    levelLoadingTimeOut: 8000,
    fragLoadingTimeOut: 10000,
    maxStarvationDelay: 4,
    maxLoadingDelay: 4
  },
  [NetworkQuality.FAIR]: {
    // More conservative for slower connections
    backBufferLength: 15,
    maxBufferLength: 12,
    maxMaxBufferLength: 20,
    liveSyncDurationCount: 3,
    liveMaxLatencyDurationCount: 6,
    manifestLoadingTimeOut: 12000,
    manifestLoadingMaxRetry: 4,
    manifestLoadingRetryDelay: 1000,
    levelLoadingTimeOut: 12000,
    fragLoadingTimeOut: 15000,
    maxStarvationDelay: 6,
    maxLoadingDelay: 6
  },
  [NetworkQuality.POOR]: {
    // Maximum reliability for poor connections
    backBufferLength: 30,
    maxBufferLength: 20,
    maxMaxBufferLength: 40,
    liveSyncDurationCount: 5,
    liveMaxLatencyDurationCount: 8,
    manifestLoadingTimeOut: 20000,
    manifestLoadingMaxRetry: 5,
    manifestLoadingRetryDelay: 2000,
    levelLoadingTimeOut: 20000,
    fragLoadingTimeOut: 25000,
    maxStarvationDelay: 10,
    maxLoadingDelay: 10
  }
}

/**
 * Get adaptive HLS configuration based on network quality
 */
export const getAdaptiveHLSConfig = (networkQuality: NetworkQuality = NetworkQuality.GOOD) => {
  const baseConfig = config.hls
  const adaptiveSettings = adaptiveHLSConfigs[networkQuality]

  return {
    ...baseConfig,
    ...adaptiveSettings
  }
}

/**
 * Detect network quality based on connection information
 * This is a simple heuristic - in production you might want more sophisticated detection
 */
export const detectNetworkQuality = (): NetworkQuality => {
  // Check if we have network connection info
  if ('connection' in navigator) {
    const connection = (navigator as any).connection

    if (connection) {
      const effectiveType = connection.effectiveType
      const downlink = connection.downlink || 0

      // Use effective connection type and downlink speed
      if (effectiveType === '4g' && downlink > 10) {
        return NetworkQuality.EXCELLENT
      } else if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 2)) {
        return NetworkQuality.GOOD
      } else if (effectiveType === '3g' || downlink > 0.5) {
        return NetworkQuality.FAIR
      } else {
        return NetworkQuality.POOR
      }
    }
  }

  // Fallback to good quality if we can't detect
  return NetworkQuality.GOOD
}

// Validation functions
export const validateStationId = (stationId: string): boolean => {
  return /^[A-Z0-9_]+$/.test(stationId)
}

export const validateApiUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Type guards
export const isValidConfig = (config: unknown): config is AppConfig => {
  if (!config || typeof config !== 'object') return false

  const cfg = config as Record<string, unknown>

  return !!(
    cfg.api &&
    typeof cfg.api === 'object' &&
    cfg.api !== null &&
    typeof (cfg.api as Record<string, unknown>).baseUrl === 'string' &&
    typeof (cfg.api as Record<string, unknown>).timeout === 'number' &&
    cfg.player &&
    typeof cfg.player === 'object' &&
    cfg.player !== null &&
    typeof (cfg.player as Record<string, unknown>).defaultStationId === 'string' &&
    typeof (cfg.player as Record<string, unknown>).autoPlay === 'boolean' &&
    cfg.hls &&
    typeof cfg.hls === 'object' &&
    cfg.hls !== null &&
    typeof (cfg.hls as Record<string, unknown>).debug === 'boolean'
  )
}

export default config
