/**
 * Configuration for the HLS Player Application
 */

export interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
  }
  player: {
    defaultStationId: string
    autoPlay: boolean
    autoRefresh: boolean
    refreshIntervals: {
      status: number
      stats: number
    }
  }
  hls: {
    debug: boolean
    enableWorker: boolean
    lowLatencyMode: boolean
    maxBufferLength: number
    liveSyncDurationCount: number
    liveMaxLatencyDurationCount: number
    liveDurationInfinity: boolean
    manifestLoadingTimeOut: number
    manifestLoadingMaxRetry: number
    manifestLoadingRetryDelay: number
    levelLoadingTimeOut: number
    fragLoadingTimeOut: number
  }
  ui: {
    theme: 'light' | 'dark' | 'system'
    showAdvancedStats: boolean
  }
}

// Default configuration
const defaultConfig: AppConfig = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://awosnew.skytraces.com',
    timeout: 10000
  },
  player: {
    defaultStationId: process.env.NEXT_PUBLIC_STATION_ID || '4FL5',
    autoPlay: false,
    autoRefresh: true,
    refreshIntervals: {
      status: 5000, // 5 seconds
      stats: 2000   // 2 seconds
    }
  },
  hls: {
    debug: process.env.NODE_ENV === 'development',
    enableWorker: true,
    lowLatencyMode: true,
    // Base settings - will be overridden by adaptive config
    maxBufferLength: 6,
    liveSyncDurationCount: 2,
    liveMaxLatencyDurationCount: 4,
    liveDurationInfinity: true,
    manifestLoadingTimeOut: 8000,
    manifestLoadingMaxRetry: 3,
    manifestLoadingRetryDelay: 750,
    levelLoadingTimeOut: 8000,
    fragLoadingTimeOut: 10000
  },
  ui: {
    theme: 'system',
    showAdvancedStats: false
  }
}

// Environment-specific overrides
const getEnvironmentConfig = (): Partial<AppConfig> => {
  const env = process.env.NODE_ENV

  switch (env) {
    case 'development':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: true
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: true
        }
      }
    
    case 'production':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: false
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: false
        }
      }
    
    default:
      return {}
  }
}

// Merge configurations
export const config: AppConfig = {
  ...defaultConfig,
  ...getEnvironmentConfig()
}

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '') // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${cleanEndpoint}`
}

export const getHLSPlaylistUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/playlist.m3u8`)
}

export const getHLSStatusUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/status`)
}

export const getHLSSegmentUrl = (stationId: string, segmentName: string): string => {
  return getApiUrl(`/hls/${stationId}/${segmentName}`)
}

/**
 * Network quality levels for adaptive HLS configuration
 */
export enum NetworkQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

/**
 * Simplified adaptive HLS configurations - only essential settings that matter
 */
export const adaptiveHLSConfigs = {
  [NetworkQuality.EXCELLENT]: {
    // Ultra-low latency for excellent connections
    maxBufferLength: 3,
    liveSyncDurationCount: 1,
    liveMaxLatencyDurationCount: 2,
    manifestLoadingTimeOut: 4000,
    fragLoadingTimeOut: 6000
  },
  [NetworkQuality.GOOD]: {
    // Balanced settings (default)
    maxBufferLength: 6,
    liveSyncDurationCount: 2,
    liveMaxLatencyDurationCount: 4,
    manifestLoadingTimeOut: 8000,
    fragLoadingTimeOut: 10000
  },
  [NetworkQuality.FAIR]: {
    // More conservative for slower connections
    maxBufferLength: 12,
    liveSyncDurationCount: 3,
    liveMaxLatencyDurationCount: 6,
    manifestLoadingTimeOut: 12000,
    fragLoadingTimeOut: 15000
  },
  [NetworkQuality.POOR]: {
    // Maximum reliability for poor connections
    maxBufferLength: 20,
    liveSyncDurationCount: 5,
    liveMaxLatencyDurationCount: 8,
    manifestLoadingTimeOut: 20000,
    fragLoadingTimeOut: 25000
  }
}

/**
 * Get adaptive HLS configuration based on network quality
 */
export const getAdaptiveHLSConfig = (networkQuality: NetworkQuality = NetworkQuality.GOOD) => {
  const baseConfig = config.hls
  const adaptiveSettings = adaptiveHLSConfigs[networkQuality]

  return {
    ...baseConfig,
    ...adaptiveSettings
  }
}

/**
 * Detect network quality based on actual API response times and reliability
 */
export const detectNetworkQualityFromAPI = async (statusUrl: string): Promise<NetworkQuality> => {
  try {
    const startTime = performance.now()
    const response = await fetch(statusUrl, {
      method: 'GET',
      cache: 'no-cache'
    })
    const endTime = performance.now()
    const responseTime = endTime - startTime

    if (!response.ok) {
      return NetworkQuality.POOR
    }

    // Classify based on API response time
    if (responseTime < 100) {
      return NetworkQuality.EXCELLENT  // < 100ms
    } else if (responseTime < 300) {
      return NetworkQuality.GOOD       // 100-300ms
    } else if (responseTime < 800) {
      return NetworkQuality.FAIR       // 300-800ms
    } else {
      return NetworkQuality.POOR       // > 800ms
    }
  } catch (error) {
    console.warn('Failed to detect network quality from API:', error)
    return NetworkQuality.POOR
  }
}

/**
 * Simple fallback network quality detection
 */
export const detectNetworkQuality = (): NetworkQuality => {
  // Default to good quality - will be overridden by API-based detection
  return NetworkQuality.GOOD
}

// Validation functions
export const validateStationId = (stationId: string): boolean => {
  return /^[A-Z0-9_]+$/.test(stationId)
}

export const validateApiUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Type guards
export const isValidConfig = (config: unknown): config is AppConfig => {
  if (!config || typeof config !== 'object') return false

  const cfg = config as Record<string, unknown>

  return !!(
    cfg.api &&
    typeof cfg.api === 'object' &&
    cfg.api !== null &&
    typeof (cfg.api as Record<string, unknown>).baseUrl === 'string' &&
    typeof (cfg.api as Record<string, unknown>).timeout === 'number' &&
    cfg.player &&
    typeof cfg.player === 'object' &&
    cfg.player !== null &&
    typeof (cfg.player as Record<string, unknown>).defaultStationId === 'string' &&
    typeof (cfg.player as Record<string, unknown>).autoPlay === 'boolean' &&
    cfg.hls &&
    typeof cfg.hls === 'object' &&
    cfg.hls !== null &&
    typeof (cfg.hls as Record<string, unknown>).debug === 'boolean'
  )
}

export default config
