# HLS Player Deployment Guide

This guide covers deploying the HLS Player application using Docker for production environments.

## Prerequisites

- Docker and Docker Compose installed
- Access to your weather API server
- Domain configured (app.skytraces.com)

## Quick Deployment

### 1. Using the Deployment Script (Recommended)

```bash
# Make the script executable (if not already)
chmod +x deploy.sh

# Deploy the application
./deploy.sh

# Other useful commands:
./deploy.sh build    # Build image only
./deploy.sh run      # Run container only
./deploy.sh logs     # View container logs
./deploy.sh stop     # Stop container
./deploy.sh clean    # Clean up containers and images
```

### 2. Manual Docker Deployment

```bash
# Build the Docker image
docker build -t hls-player .

# Run the container
docker run -d \
  --name hls-player-app \
  -p 3000:3000 \
  --env-file .env.local \
  --restart unless-stopped \
  hls-player
```

### 3. Docker Compose Deployment

```bash
# Set environment variables
export NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
export NEXT_PUBLIC_STATION_ID=4FL5

# Deploy with Docker Compose
docker-compose up -d
```

## Environment Configuration

### For app.skytraces.com Deployment

Create or update `.env.local`:

```env
# Production API Configuration
NEXT_PUBLIC_API_BASE_URL=https://awosnew.skytraces.com
NEXT_PUBLIC_STATION_ID=4FL5

# Production settings
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
```

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_API_BASE_URL` | Weather API base URL | `http://localhost:8000` | Yes |
| `NEXT_PUBLIC_STATION_ID` | Station identifier | `4FL5` | Yes |
| `NODE_ENV` | Environment mode | `production` | No |
| `NEXT_TELEMETRY_DISABLED` | Disable Next.js telemetry | `1` | No |

## CORS Configuration

The weather API has been updated to allow the following origins:

- `http://localhost:3000` (development)
- `http://localhost:3001` (development)
- `https://app.skytraces.com` (production)
- `https://skytraces.com` (production)

### Adding Additional Origins

Set the `ADDITIONAL_CORS_ORIGINS` environment variable in your weather API:

```env
ADDITIONAL_CORS_ORIGINS=https://your-custom-domain.com,https://another-domain.com
```

## Production Deployment Steps

### 1. Prepare the Server

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Deploy the Application

```bash
# Clone the repository
git clone <your-repo-url>
cd hls-player-app

# Configure environment
cp .env.production .env.local
# Edit .env.local with your production values

# Deploy
./deploy.sh
```

### 3. Set up Reverse Proxy (Nginx/Traefik)

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name app.skytraces.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name app.skytraces.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### Traefik Configuration (docker-compose.yml included)

The included `docker-compose.yml` has Traefik labels configured for automatic SSL and routing.

## Monitoring and Maintenance

### Health Checks

The application includes a health check endpoint:

```bash
curl http://localhost:3000/api/health
```

### Viewing Logs

```bash
# View container logs
docker logs -f hls-player-app

# Or using the deployment script
./deploy.sh logs
```

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Rebuild and redeploy
./deploy.sh
```

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure your domain is added to the weather API CORS configuration
   - Check that the API URL is correct in environment variables

2. **Container Won't Start**
   - Check Docker logs: `docker logs hls-player-app`
   - Verify environment variables are set correctly
   - Ensure port 3000 is not already in use

3. **Health Check Fails**
   - Wait 30-60 seconds for the application to fully start
   - Check if the container is running: `docker ps`
   - Verify the health endpoint: `curl http://localhost:3000/api/health`

4. **Audio Not Playing**
   - Check browser console for errors
   - Verify the weather API is accessible from the client
   - Ensure HLS streams are being generated by the weather API

### Performance Optimization

1. **Enable Gzip Compression** (in reverse proxy)
2. **Set up CDN** for static assets
3. **Configure proper caching headers**
4. **Monitor resource usage** with Docker stats

## Security Considerations

1. **Use HTTPS** in production
2. **Keep Docker images updated**
3. **Limit CORS origins** to necessary domains only
4. **Use environment variables** for sensitive configuration
5. **Regular security updates** for the host system

## Backup and Recovery

### Backup

```bash
# Export Docker image
docker save hls-player > hls-player-backup.tar

# Backup configuration
tar -czf config-backup.tar.gz .env.local docker-compose.yml
```

### Recovery

```bash
# Import Docker image
docker load < hls-player-backup.tar

# Restore configuration
tar -xzf config-backup.tar.gz

# Redeploy
./deploy.sh
```
