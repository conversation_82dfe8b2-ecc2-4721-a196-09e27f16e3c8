# Ridge Landing Airpark - AWOS HLS Player

A modern, responsive web application for streaming live AWOS (Automated Weather Observing System) audio from Ridge Landing Airpark (VPS) using HLS (HTTP Live Streaming) technology.

## Features

- **Live HLS Audio Streaming**: Real-time AWOS audio playback using HLS.js
- **Modern UI**: Clean, responsive interface built with shadcn/ui components
- **Real-time Status**: Live stream status monitoring and statistics
- **Error Handling**: Comprehensive error handling with automatic recovery
- **Mobile Responsive**: Optimized for desktop and mobile devices
- **TypeScript**: Full type safety throughout the application
- **Configuration**: Environment-based configuration system

## Technology Stack

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **HLS.js**: HTTP Live Streaming client
- **shadcn/ui**: Modern UI component library
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful icons

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Access to the weather API container endpoints

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd hls-player-app
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Configure environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_STATION_ID=4FL5
```

4. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Configuration

The application uses a centralized configuration system located in `src/lib/config.ts`. Key configuration options include:

- **API Settings**: Base URL, timeout, station ID
- **HLS Settings**: Buffer lengths, retry policies, latency settings
- **UI Settings**: Theme, refresh intervals, advanced stats

## API Endpoints

The application expects the following endpoints from the weather API container:

- `GET /hls/{station_id}/playlist.m3u8` - HLS playlist
- `GET /hls/{station_id}/status` - Stream status
- `GET /hls/{station_id}/{segment_name}` - HLS segments

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── hls-player.tsx  # Main HLS player component
│   └── error-boundary.tsx # Error boundary component
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and configuration
├── types/              # TypeScript type definitions
└── styles/             # Global styles
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

### Adding Components

Use shadcn/ui CLI to add new components:
```bash
npx shadcn@latest add <component-name>
```

## Deployment

### Quick Docker Deployment

The application includes Docker support for easy deployment:

```bash
# Quick deployment using the included script
./deploy.sh

# Or manually with Docker
docker build -t hls-player .
docker run -d --name hls-player-app -p 3000:3000 --env-file .env.local hls-player
```

### Environment Variables

For production deployment, update `.env.local`:

```env
# Production Configuration
NEXT_PUBLIC_API_BASE_URL=https://your-weather-api-domain.com
NEXT_PUBLIC_STATION_ID=4FL5
NODE_ENV=production
```

### CORS Configuration

The weather API has been updated to support:
- `http://localhost:3000` (development)
- `http://localhost:3001` (development)
- `https://app.skytraces.com` (production)
- `https://skytraces.com` (production)

### Deployment Options

1. **Docker (Recommended)**: Use the included Dockerfile and deployment script
2. **Docker Compose**: Use `docker-compose up -d` for orchestrated deployment
3. **Traditional**: Use `npm run build && npm run start`

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

## Browser Support

- Chrome/Chromium 60+
- Firefox 55+
- Safari 11+
- Edge 79+

HLS.js provides broad browser support, with native HLS support in Safari and HLS.js fallback for other browsers.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please open an issue in the repository or contact the Ridge Landing Airpark Weather System team.
